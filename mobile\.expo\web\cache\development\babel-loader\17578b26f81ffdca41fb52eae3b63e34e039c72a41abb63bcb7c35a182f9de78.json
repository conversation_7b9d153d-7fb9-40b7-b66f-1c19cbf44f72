{"ast": null, "code": "import * as React from 'react';\nimport View from \"../../../../exports/View\";\nimport createAnimatedComponent from \"../createAnimatedComponent\";\nexport default createAnimatedComponent(View);", "map": {"version": 3, "names": ["React", "View", "createAnimatedComponent"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/vendor/react-native/Animated/components/AnimatedView.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport View from '../../../../exports/View';\nimport createAnimatedComponent from '../createAnimatedComponent';\nexport default createAnimatedComponent(View);"], "mappings": "AAUA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI;AACX,OAAOC,uBAAuB;AAC9B,eAAeA,uBAAuB,CAACD,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}