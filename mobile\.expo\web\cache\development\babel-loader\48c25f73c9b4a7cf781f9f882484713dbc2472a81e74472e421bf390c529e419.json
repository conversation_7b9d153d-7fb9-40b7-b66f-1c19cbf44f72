{"ast": null, "code": "import { get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HeaderBackContext, SafeAreaProviderCompat, Screen } from '@react-navigation/elements';\nimport * as React from 'react';\nimport Image from \"react-native-web/dist/exports/Image\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nvar TRANSPARENT_PRESENTATIONS = ['transparentModal', 'containedTransparentModal'];\nexport default function NativeStackView(_ref) {\n  var state = _ref.state,\n    descriptors = _ref.descriptors;\n  var parentHeaderBack = React.useContext(HeaderBackContext);\n  return React.createElement(SafeAreaProviderCompat, null, React.createElement(View, {\n    style: styles.container\n  }, state.routes.map(function (route, i) {\n    var _state$routes, _state$routes2;\n    var isFocused = state.index === i;\n    var previousKey = (_state$routes = state.routes[i - 1]) === null || _state$routes === void 0 ? void 0 : _state$routes.key;\n    var nextKey = (_state$routes2 = state.routes[i + 1]) === null || _state$routes2 === void 0 ? void 0 : _state$routes2.key;\n    var previousDescriptor = previousKey ? descriptors[previousKey] : undefined;\n    var nextDescriptor = nextKey ? descriptors[nextKey] : undefined;\n    var _descriptors$route$ke = descriptors[route.key],\n      options = _descriptors$route$ke.options,\n      navigation = _descriptors$route$ke.navigation,\n      render = _descriptors$route$ke.render;\n    var headerBack = previousDescriptor ? {\n      title: getHeaderTitle(previousDescriptor.options, previousDescriptor.route.name)\n    } : parentHeaderBack;\n    var canGoBack = headerBack !== undefined;\n    var header = options.header,\n      headerShown = options.headerShown,\n      headerTintColor = options.headerTintColor,\n      headerBackImageSource = options.headerBackImageSource,\n      headerLeft = options.headerLeft,\n      headerRight = options.headerRight,\n      headerTitle = options.headerTitle,\n      headerTitleAlign = options.headerTitleAlign,\n      headerTitleStyle = options.headerTitleStyle,\n      headerStyle = options.headerStyle,\n      headerShadowVisible = options.headerShadowVisible,\n      headerTransparent = options.headerTransparent,\n      headerBackground = options.headerBackground,\n      headerBackTitle = options.headerBackTitle,\n      presentation = options.presentation,\n      contentStyle = options.contentStyle;\n    var nextPresentation = nextDescriptor === null || nextDescriptor === void 0 ? void 0 : nextDescriptor.options.presentation;\n    return React.createElement(Screen, {\n      key: route.key,\n      focused: isFocused,\n      route: route,\n      navigation: navigation,\n      headerShown: headerShown,\n      headerTransparent: headerTransparent,\n      header: header !== undefined ? header({\n        back: headerBack,\n        options: options,\n        route: route,\n        navigation: navigation\n      }) : React.createElement(Header, {\n        title: getHeaderTitle(options, route.name),\n        headerTintColor: headerTintColor,\n        headerLeft: typeof headerLeft === 'function' ? function (_ref2) {\n          var tintColor = _ref2.tintColor;\n          return headerLeft({\n            tintColor: tintColor,\n            canGoBack: canGoBack,\n            label: headerBackTitle\n          });\n        } : headerLeft === undefined && canGoBack ? function (_ref3) {\n          var tintColor = _ref3.tintColor;\n          return React.createElement(HeaderBackButton, {\n            tintColor: tintColor,\n            backImage: headerBackImageSource !== undefined ? function () {\n              return React.createElement(Image, {\n                source: headerBackImageSource,\n                style: [styles.backImage, {\n                  tintColor: tintColor\n                }]\n              });\n            } : undefined,\n            onPress: navigation.goBack,\n            canGoBack: canGoBack\n          });\n        } : headerLeft,\n        headerRight: typeof headerRight === 'function' ? function (_ref4) {\n          var tintColor = _ref4.tintColor;\n          return headerRight({\n            tintColor: tintColor,\n            canGoBack: canGoBack\n          });\n        } : headerRight,\n        headerTitle: typeof headerTitle === 'function' ? function (_ref5) {\n          var children = _ref5.children,\n            tintColor = _ref5.tintColor;\n          return headerTitle({\n            children: children,\n            tintColor: tintColor\n          });\n        } : headerTitle,\n        headerTitleAlign: headerTitleAlign,\n        headerTitleStyle: headerTitleStyle,\n        headerTransparent: headerTransparent,\n        headerShadowVisible: headerShadowVisible,\n        headerBackground: headerBackground,\n        headerStyle: headerStyle\n      }),\n      style: [StyleSheet.absoluteFill, {\n        display: isFocused || nextPresentation != null && TRANSPARENT_PRESENTATIONS.includes(nextPresentation) ? 'flex' : 'none'\n      }, presentation != null && TRANSPARENT_PRESENTATIONS.includes(presentation) ? {\n        backgroundColor: 'transparent'\n      } : null]\n    }, React.createElement(HeaderBackContext.Provider, {\n      value: headerBack\n    }, React.createElement(View, {\n      style: [styles.contentContainer, contentStyle]\n    }, render())));\n  })));\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1\n  },\n  contentContainer: {\n    flex: 1\n  },\n  backImage: {\n    height: 24,\n    width: 24,\n    margin: 3,\n    resizeMode: 'contain'\n  }\n});", "map": {"version": 3, "names": ["getHeaderTitle", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HeaderBackContext", "SafeAreaProviderCompat", "Screen", "React", "Image", "StyleSheet", "View", "TRANSPARENT_PRESENTATIONS", "NativeStackView", "_ref", "state", "descriptors", "parentHeaderBack", "useContext", "createElement", "style", "styles", "container", "routes", "map", "route", "i", "_state$routes", "_state$routes2", "isFocused", "index", "previousKey", "key", "<PERSON><PERSON><PERSON>", "previousDescriptor", "undefined", "nextDescriptor", "_descriptors$route$ke", "options", "navigation", "render", "headerBack", "title", "name", "canGoBack", "header", "headerShown", "headerTintColor", "headerBackImageSource", "headerLeft", "headerRight", "headerTitle", "headerTitleAlign", "headerTitleStyle", "headerStyle", "headerShadowVisible", "headerTransparent", "headerBackground", "headerBackTitle", "presentation", "contentStyle", "nextPresentation", "focused", "back", "_ref2", "tintColor", "label", "_ref3", "backImage", "source", "onPress", "goBack", "_ref4", "_ref5", "children", "absoluteFill", "display", "includes", "backgroundColor", "Provider", "value", "contentContainer", "create", "flex", "height", "width", "margin", "resizeMode"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native-stack\\src\\views\\NativeStackView.tsx"], "sourcesContent": ["import {\n  get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>erBack<PERSON>utton,\n  HeaderBackContext,\n  SafeAreaProviderCompat,\n  Screen,\n} from '@react-navigation/elements';\nimport type {\n  ParamListBase,\n  StackNavigationState,\n} from '@react-navigation/native';\nimport * as React from 'react';\nimport { Image, StyleSheet, View } from 'react-native';\n\nimport type {\n  NativeStackDescriptorMap,\n  NativeStackNavigationHelpers,\n} from '../types';\n\ntype Props = {\n  state: StackNavigationState<ParamListBase>;\n  // This is used for the native implementation of the stack.\n  // eslint-disable-next-line react/no-unused-prop-types\n  navigation: NativeStackNavigationHelpers;\n  descriptors: NativeStackDescriptorMap;\n};\n\nconst TRANSPARENT_PRESENTATIONS = [\n  'transparentModal',\n  'containedTransparentModal',\n];\n\nexport default function NativeStackView({ state, descriptors }: Props) {\n  const parentHeaderBack = React.useContext(HeaderBackContext);\n\n  return (\n    <SafeAreaProviderCompat>\n      <View style={styles.container}>\n        {state.routes.map((route, i) => {\n          const isFocused = state.index === i;\n          const previousKey = state.routes[i - 1]?.key;\n          const nextKey = state.routes[i + 1]?.key;\n          const previousDescriptor = previousKey\n            ? descriptors[previousKey]\n            : undefined;\n          const nextDescriptor = nextKey ? descriptors[nextKey] : undefined;\n          const { options, navigation, render } = descriptors[route.key];\n\n          const headerBack = previousDescriptor\n            ? {\n                title: getHeaderTitle(\n                  previousDescriptor.options,\n                  previousDescriptor.route.name\n                ),\n              }\n            : parentHeaderBack;\n\n          const canGoBack = headerBack !== undefined;\n\n          const {\n            header,\n            headerShown,\n            headerTintColor,\n            headerBackImageSource,\n            headerLeft,\n            headerRight,\n            headerTitle,\n            headerTitleAlign,\n            headerTitleStyle,\n            headerStyle,\n            headerShadowVisible,\n            headerTransparent,\n            headerBackground,\n            headerBackTitle,\n            presentation,\n            contentStyle,\n          } = options;\n\n          const nextPresentation = nextDescriptor?.options.presentation;\n\n          return (\n            <Screen\n              key={route.key}\n              focused={isFocused}\n              route={route}\n              navigation={navigation}\n              headerShown={headerShown}\n              headerTransparent={headerTransparent}\n              header={\n                header !== undefined ? (\n                  header({\n                    back: headerBack,\n                    options,\n                    route,\n                    navigation,\n                  })\n                ) : (\n                  <Header\n                    title={getHeaderTitle(options, route.name)}\n                    headerTintColor={headerTintColor}\n                    headerLeft={\n                      typeof headerLeft === 'function'\n                        ? ({ tintColor }) =>\n                            headerLeft({\n                              tintColor,\n                              canGoBack,\n                              label: headerBackTitle,\n                            })\n                        : headerLeft === undefined && canGoBack\n                        ? ({ tintColor }) => (\n                            <HeaderBackButton\n                              tintColor={tintColor}\n                              backImage={\n                                headerBackImageSource !== undefined\n                                  ? () => (\n                                      <Image\n                                        source={headerBackImageSource}\n                                        style={[\n                                          styles.backImage,\n                                          { tintColor },\n                                        ]}\n                                      />\n                                    )\n                                  : undefined\n                              }\n                              onPress={navigation.goBack}\n                              canGoBack={canGoBack}\n                            />\n                          )\n                        : headerLeft\n                    }\n                    headerRight={\n                      typeof headerRight === 'function'\n                        ? ({ tintColor }) =>\n                            headerRight({ tintColor, canGoBack })\n                        : headerRight\n                    }\n                    headerTitle={\n                      typeof headerTitle === 'function'\n                        ? ({ children, tintColor }) =>\n                            headerTitle({ children, tintColor })\n                        : headerTitle\n                    }\n                    headerTitleAlign={headerTitleAlign}\n                    headerTitleStyle={headerTitleStyle}\n                    headerTransparent={headerTransparent}\n                    headerShadowVisible={headerShadowVisible}\n                    headerBackground={headerBackground}\n                    headerStyle={headerStyle}\n                  />\n                )\n              }\n              style={[\n                StyleSheet.absoluteFill,\n                {\n                  display:\n                    isFocused ||\n                    (nextPresentation != null &&\n                      TRANSPARENT_PRESENTATIONS.includes(nextPresentation))\n                      ? 'flex'\n                      : 'none',\n                },\n                presentation != null &&\n                TRANSPARENT_PRESENTATIONS.includes(presentation)\n                  ? { backgroundColor: 'transparent' }\n                  : null,\n              ]}\n            >\n              <HeaderBackContext.Provider value={headerBack}>\n                <View style={[styles.contentContainer, contentStyle]}>\n                  {render()}\n                </View>\n              </HeaderBackContext.Provider>\n            </Screen>\n          );\n        })}\n      </View>\n    </SafeAreaProviderCompat>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  contentContainer: {\n    flex: 1,\n  },\n  backImage: {\n    height: 24,\n    width: 24,\n    margin: 3,\n    resizeMode: 'contain',\n  },\n});\n"], "mappings": "AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,gBAAgB,EAChBC,iBAAiB,EACjBC,sBAAsB,EACtBC,MAAM,QACD,4BAA4B;AAKnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,KAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAgB9B,IAAMC,yBAAyB,GAAG,CAChC,kBAAkB,EAClB,2BAA2B,CAC5B;AAED,eAAe,SAASC,eAAeA,CAAAC,IAAA,EAAgC;EAAA,IAA7BC,KAAK,GAAsBD,IAAA,CAA3BC,KAAK;IAAEC,WAAA,GAAoBF,IAAA,CAApBE,WAAA;EAC/C,IAAMC,gBAAgB,GAAGT,KAAK,CAACU,UAAU,CAACb,iBAAiB,CAAC;EAE5D,OACEG,KAAA,CAAAW,aAAA,CAACb,sBAAsB,QACrBE,KAAA,CAAAW,aAAA,CAACR,IAAI;IAACS,KAAK,EAAEC,MAAM,CAACC;EAAU,GAC3BP,KAAK,CAACQ,MAAM,CAACC,GAAG,CAAC,UAACC,KAAK,EAAEC,CAAC,EAAK;IAAA,IAAAC,aAAA,EAAAC,cAAA;IAC9B,IAAMC,SAAS,GAAGd,KAAK,CAACe,KAAK,KAAKJ,CAAC;IACnC,IAAMK,WAAW,IAAAJ,aAAA,GAAGZ,KAAK,CAACQ,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,cAAAC,aAAA,uBAAnBA,aAAA,CAAqBK,GAAG;IAC5C,IAAMC,OAAO,IAAAL,cAAA,GAAGb,KAAK,CAACQ,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,cAAAE,cAAA,uBAAnBA,cAAA,CAAqBI,GAAG;IACxC,IAAME,kBAAkB,GAAGH,WAAW,GAClCf,WAAW,CAACe,WAAW,CAAC,GACxBI,SAAS;IACb,IAAMC,cAAc,GAAGH,OAAO,GAAGjB,WAAW,CAACiB,OAAO,CAAC,GAAGE,SAAS;IACjE,IAAAE,qBAAA,GAAwCrB,WAAW,CAACS,KAAK,CAACO,GAAG,CAAC;MAAtDM,OAAO,GAAAD,qBAAA,CAAPC,OAAO;MAAEC,UAAU,GAAAF,qBAAA,CAAVE,UAAU;MAAEC,MAAA,GAAAH,qBAAA,CAAAG,MAAA;IAE7B,IAAMC,UAAU,GAAGP,kBAAkB,GACjC;MACEQ,KAAK,EAAExC,cAAc,CACnBgC,kBAAkB,CAACI,OAAO,EAC1BJ,kBAAkB,CAACT,KAAK,CAACkB,IAAI;IAEjC,CAAC,GACD1B,gBAAgB;IAEpB,IAAM2B,SAAS,GAAGH,UAAU,KAAKN,SAAS;IAE1C,IACEU,MAAM,GAgBJP,OAAO,CAhBTO,MAAM;MACNC,WAAW,GAeTR,OAAO,CAfTQ,WAAW;MACXC,eAAe,GAcbT,OAAO,CAdTS,eAAe;MACfC,qBAAqB,GAanBV,OAAO,CAbTU,qBAAqB;MACrBC,UAAU,GAYRX,OAAO,CAZTW,UAAU;MACVC,WAAW,GAWTZ,OAAO,CAXTY,WAAW;MACXC,WAAW,GAUTb,OAAO,CAVTa,WAAW;MACXC,gBAAgB,GASdd,OAAO,CATTc,gBAAgB;MAChBC,gBAAgB,GAQdf,OAAO,CARTe,gBAAgB;MAChBC,WAAW,GAOThB,OAAO,CAPTgB,WAAW;MACXC,mBAAmB,GAMjBjB,OAAO,CANTiB,mBAAmB;MACnBC,iBAAiB,GAKflB,OAAO,CALTkB,iBAAiB;MACjBC,gBAAgB,GAIdnB,OAAO,CAJTmB,gBAAgB;MAChBC,eAAe,GAGbpB,OAAO,CAHToB,eAAe;MACfC,YAAY,GAEVrB,OAAO,CAFTqB,YAAY;MACZC,YAAA,GACEtB,OAAO,CADTsB,YAAA;IAGF,IAAMC,gBAAgB,GAAGzB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,OAAO,CAACqB,YAAY;IAE7D,OACEnD,KAAA,CAAAW,aAAA,CAACZ,MAAM;MACLyB,GAAG,EAAEP,KAAK,CAACO,GAAI;MACf8B,OAAO,EAAEjC,SAAU;MACnBJ,KAAK,EAAEA,KAAM;MACbc,UAAU,EAAEA,UAAW;MACvBO,WAAW,EAAEA,WAAY;MACzBU,iBAAiB,EAAEA,iBAAkB;MACrCX,MAAM,EACJA,MAAM,KAAKV,SAAS,GAClBU,MAAM,CAAC;QACLkB,IAAI,EAAEtB,UAAU;QAChBH,OAAO,EAAPA,OAAO;QACPb,KAAK,EAALA,KAAK;QACLc,UAAA,EAAAA;MACF,CAAC,CAAC,GAEF/B,KAAA,CAAAW,aAAA,CAAChB,MAAM;QACLuC,KAAK,EAAExC,cAAc,CAACoC,OAAO,EAAEb,KAAK,CAACkB,IAAI,CAAE;QAC3CI,eAAe,EAAEA,eAAgB;QACjCE,UAAU,EACR,OAAOA,UAAU,KAAK,UAAU,GAC5B,UAAAe,KAAA;UAAA,IAAGC,SAAA,GAAWD,KAAA,CAAXC,SAAA;UAAW,OACZhB,UAAU,CAAC;YACTgB,SAAS,EAATA,SAAS;YACTrB,SAAS,EAATA,SAAS;YACTsB,KAAK,EAAER;UACT,CAAC,CAAC;QAAA,IACJT,UAAU,KAAKd,SAAS,IAAIS,SAAS,GACrC,UAAAuB,KAAA;UAAA,IAAGF,SAAA,GAAWE,KAAA,CAAXF,SAAA;UAAW,OACZzD,KAAA,CAAAW,aAAA,CAACf,gBAAgB;YACf6D,SAAS,EAAEA,SAAU;YACrBG,SAAS,EACPpB,qBAAqB,KAAKb,SAAS,GAC/B;cAAA,OACE3B,KAAA,CAAAW,aAAA,CAACV,KAAK;gBACJ4D,MAAM,EAAErB,qBAAsB;gBAC9B5B,KAAK,EAAE,CACLC,MAAM,CAAC+C,SAAS,EAChB;kBAAEH,SAAA,EAAAA;gBAAU,CAAC;cACb,EAEL;YAAA,IACD9B,SACL;YACDmC,OAAO,EAAE/B,UAAU,CAACgC,MAAO;YAC3B3B,SAAS,EAAEA;UAAU,EACrB;QAAA,CACH,GACDK,UACL;QACDC,WAAW,EACT,OAAOA,WAAW,KAAK,UAAU,GAC7B,UAAAsB,KAAA;UAAA,IAAGP,SAAA,GAAWO,KAAA,CAAXP,SAAA;UAAW,OACZf,WAAW,CAAC;YAAEe,SAAS,EAATA,SAAS;YAAErB,SAAA,EAAAA;UAAU,CAAC,CAAC;QAAA,IACvCM,WACL;QACDC,WAAW,EACT,OAAOA,WAAW,KAAK,UAAU,GAC7B,UAAAsB,KAAA;UAAA,IAAGC,QAAQ,GAAaD,KAAA,CAArBC,QAAQ;YAAET,SAAA,GAAWQ,KAAA,CAAXR,SAAA;UAAW,OACtBd,WAAW,CAAC;YAAEuB,QAAQ,EAARA,QAAQ;YAAET,SAAA,EAAAA;UAAU,CAAC,CAAC;QAAA,IACtCd,WACL;QACDC,gBAAgB,EAAEA,gBAAiB;QACnCC,gBAAgB,EAAEA,gBAAiB;QACnCG,iBAAiB,EAAEA,iBAAkB;QACrCD,mBAAmB,EAAEA,mBAAoB;QACzCE,gBAAgB,EAAEA,gBAAiB;QACnCH,WAAW,EAAEA;MAAY,EAG9B;MACDlC,KAAK,EAAE,CACLV,UAAU,CAACiE,YAAY,EACvB;QACEC,OAAO,EACL/C,SAAS,IACRgC,gBAAgB,IAAI,IAAI,IACvBjD,yBAAyB,CAACiE,QAAQ,CAAChB,gBAAgB,CAAE,GACnD,MAAM,GACN;MACR,CAAC,EACDF,YAAY,IAAI,IAAI,IACpB/C,yBAAyB,CAACiE,QAAQ,CAAClB,YAAY,CAAC,GAC5C;QAAEmB,eAAe,EAAE;MAAc,CAAC,GAClC,IAAI;IACR,GAEFtE,KAAA,CAAAW,aAAA,CAACd,iBAAiB,CAAC0E,QAAQ;MAACC,KAAK,EAAEvC;IAAW,GAC5CjC,KAAA,CAAAW,aAAA,CAACR,IAAI;MAACS,KAAK,EAAE,CAACC,MAAM,CAAC4D,gBAAgB,EAAErB,YAAY;IAAE,GAClDpB,MAAM,EAAE,CACJ,CACoB,CACtB;EAEb,CAAC,CAAC,CACG,CACgB;AAE7B;AAEA,IAAMnB,MAAM,GAAGX,UAAU,CAACwE,MAAM,CAAC;EAC/B5D,SAAS,EAAE;IACT6D,IAAI,EAAE;EACR,CAAC;EACDF,gBAAgB,EAAE;IAChBE,IAAI,EAAE;EACR,CAAC;EACDf,SAAS,EAAE;IACTgB,MAAM,EAAE,EAAE;IACVC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}