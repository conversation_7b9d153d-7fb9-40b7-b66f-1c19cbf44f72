{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport FlatList from \"react-native-web/dist/exports/FlatList\";\nimport { useNavigation } from '@react-navigation/native';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SearchScreen = function SearchScreen() {\n  var _useState = useState(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    searchQuery = _useState2[0],\n    setSearchQuery = _useState2[1];\n  var navigation = useNavigation();\n  var mockGasStations = [{\n    id: '1',\n    name: 'Shell Gas Station',\n    address: '123 Main St, City',\n    distance: '2.3 km',\n    price: '$3.45'\n  }, {\n    id: '2',\n    name: 'BP Gas Station',\n    address: '456 Oak Ave, City',\n    distance: '3.1 km',\n    price: '$3.42'\n  }, {\n    id: '3',\n    name: 'Exxon Gas Station',\n    address: '789 Pine Rd, City',\n    distance: '5.2 km',\n    price: '$3.38'\n  }];\n  var renderGasStationItem = function renderGasStationItem(_ref) {\n    var item = _ref.item;\n    return _jsxs(TouchableOpacity, {\n      style: styles.stationItem,\n      onPress: function onPress() {\n        return navigation.navigate('GasStationDetail', {\n          gasStation: item\n        });\n      },\n      children: [_jsx(Text, {\n        style: styles.stationName,\n        children: item.name\n      }), _jsx(Text, {\n        style: styles.stationAddress,\n        children: item.address\n      }), _jsxs(View, {\n        style: styles.stationInfo,\n        children: [_jsx(Text, {\n          style: styles.stationDistance,\n          children: item.distance\n        }), _jsx(Text, {\n          style: styles.stationPrice,\n          children: item.price\n        })]\n      })]\n    });\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(TextInput, {\n      style: styles.searchInput,\n      placeholder: \"Search gas stations...\",\n      value: searchQuery,\n      onChangeText: setSearchQuery\n    }), _jsx(FlatList, {\n      data: mockGasStations,\n      renderItem: renderGasStationItem,\n      keyExtractor: function keyExtractor(item) {\n        return item.id;\n      },\n      contentContainerStyle: styles.listContainer\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  searchInput: {\n    backgroundColor: 'white',\n    padding: 15,\n    margin: 10,\n    borderRadius: 8,\n    borderWidth: 1,\n    borderColor: '#ddd'\n  },\n  listContainer: {\n    padding: 10\n  },\n  stationItem: {\n    backgroundColor: 'white',\n    padding: 15,\n    marginBottom: 10,\n    borderRadius: 8,\n    borderWidth: 1,\n    borderColor: '#eee'\n  },\n  stationName: {\n    fontSize: 18,\n    fontWeight: '600',\n    marginBottom: 5\n  },\n  stationAddress: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 10\n  },\n  stationInfo: {\n    flexDirection: 'row',\n    justifyContent: 'space-between'\n  },\n  stationDistance: {\n    fontSize: 14,\n    color: '#007bff'\n  },\n  stationPrice: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#28a745'\n  }\n});\nexport default SearchScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "StyleSheet", "TextInput", "TouchableOpacity", "FlatList", "useNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "SearchScreen", "_useState", "_useState2", "_slicedToArray", "searchQuery", "setSearch<PERSON>uery", "navigation", "mockGasStations", "id", "name", "address", "distance", "price", "renderGasStationItem", "_ref", "item", "style", "styles", "stationItem", "onPress", "navigate", "gasStation", "children", "stationName", "stationAddress", "stationInfo", "stationDistance", "stationPrice", "container", "searchInput", "placeholder", "value", "onChangeText", "data", "renderItem", "keyExtractor", "contentContainerStyle", "listContainer", "create", "flex", "backgroundColor", "padding", "margin", "borderRadius", "borderWidth", "borderColor", "marginBottom", "fontSize", "fontWeight", "color", "flexDirection", "justifyContent"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/screens/SearchScreen.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { View, Text, StyleSheet, TextInput, TouchableOpacity, FlatList } from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\n\nconst SearchScreen = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const navigation = useNavigation();\n\n  // Mock data for demonstration\n  const mockGasStations = [\n    {\n      id: '1',\n      name: 'Shell Gas Station',\n      address: '123 Main St, City',\n      distance: '2.3 km',\n      price: '$3.45',\n    },\n    {\n      id: '2',\n      name: 'BP Gas Station',\n      address: '456 Oak Ave, City',\n      distance: '3.1 km',\n      price: '$3.42',\n    },\n    {\n      id: '3',\n      name: 'Exxon Gas Station',\n      address: '789 Pine Rd, City',\n      distance: '5.2 km',\n      price: '$3.38',\n    },\n  ];\n\n  const renderGasStationItem = ({ item }) => (\n    <TouchableOpacity \n      style={styles.stationItem}\n      onPress={() => navigation.navigate('GasStationDetail', { gasStation: item })}\n    >\n      <Text style={styles.stationName}>{item.name}</Text>\n      <Text style={styles.stationAddress}>{item.address}</Text>\n      <View style={styles.stationInfo}>\n        <Text style={styles.stationDistance}>{item.distance}</Text>\n        <Text style={styles.stationPrice}>{item.price}</Text>\n      </View>\n    </TouchableOpacity>\n  );\n\n  return (\n    <View style={styles.container}>\n      <TextInput\n        style={styles.searchInput}\n        placeholder=\"Search gas stations...\"\n        value={searchQuery}\n        onChangeText={setSearchQuery}\n      />\n      \n      <FlatList\n        data={mockGasStations}\n        renderItem={renderGasStationItem}\n        keyExtractor={item => item.id}\n        contentContainerStyle={styles.listContainer}\n      />\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  searchInput: {\n    backgroundColor: 'white',\n    padding: 15,\n    margin: 10,\n    borderRadius: 8,\n    borderWidth: 1,\n    borderColor: '#ddd',\n  },\n  listContainer: {\n    padding: 10,\n  },\n  stationItem: {\n    backgroundColor: 'white',\n    padding: 15,\n    marginBottom: 10,\n    borderRadius: 8,\n    borderWidth: 1,\n    borderColor: '#eee',\n  },\n  stationName: {\n    fontSize: 18,\n    fontWeight: '600',\n    marginBottom: 5,\n  },\n  stationAddress: {\n    fontSize: 14,\n    color: '#666',\n    marginBottom: 10,\n  },\n  stationInfo: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  stationDistance: {\n    fontSize: 14,\n    color: '#007bff',\n  },\n  stationPrice: {\n    fontSize: 16,\n    fontWeight: 'bold',\n    color: '#28a745',\n  },\n});\n\nexport default SearchScreen;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAAA,OAAAC,QAAA;AAExC,SAASC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzD,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EACzB,IAAAC,SAAA,GAAsCb,QAAQ,CAAC,EAAE,CAAC;IAAAc,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA3CG,WAAW,GAAAF,UAAA;IAAEG,cAAc,GAAAH,UAAA;EAClC,IAAMI,UAAU,GAAGX,aAAa,CAAC,CAAC;EAGlC,IAAMY,eAAe,GAAG,CACtB;IACEC,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,mBAAmB;IAC5BC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,gBAAgB;IACtBC,OAAO,EAAE,mBAAmB;IAC5BC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEJ,EAAE,EAAE,GAAG;IACPC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE,mBAAmB;IAC5BC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAAC,IAAA;IAAA,IAAMC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAA,OAClChB,KAAA,CAACN,gBAAgB;MACfuB,KAAK,EAAEC,MAAM,CAACC,WAAY;MAC1BC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQb,UAAU,CAACc,QAAQ,CAAC,kBAAkB,EAAE;UAAEC,UAAU,EAAEN;QAAK,CAAC,CAAC;MAAA,CAAC;MAAAO,QAAA,GAE7EzB,IAAA,CAACP,IAAI;QAAC0B,KAAK,EAAEC,MAAM,CAACM,WAAY;QAAAD,QAAA,EAAEP,IAAI,CAACN;MAAI,CAAO,CAAC,EACnDZ,IAAA,CAACP,IAAI;QAAC0B,KAAK,EAAEC,MAAM,CAACO,cAAe;QAAAF,QAAA,EAAEP,IAAI,CAACL;MAAO,CAAO,CAAC,EACzDX,KAAA,CAACV,IAAI;QAAC2B,KAAK,EAAEC,MAAM,CAACQ,WAAY;QAAAH,QAAA,GAC9BzB,IAAA,CAACP,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACS,eAAgB;UAAAJ,QAAA,EAAEP,IAAI,CAACJ;QAAQ,CAAO,CAAC,EAC3Dd,IAAA,CAACP,IAAI;UAAC0B,KAAK,EAAEC,MAAM,CAACU,YAAa;UAAAL,QAAA,EAAEP,IAAI,CAACH;QAAK,CAAO,CAAC;MAAA,CACjD,CAAC;IAAA,CACS,CAAC;EAAA,CACpB;EAED,OACEb,KAAA,CAACV,IAAI;IAAC2B,KAAK,EAAEC,MAAM,CAACW,SAAU;IAAAN,QAAA,GAC5BzB,IAAA,CAACL,SAAS;MACRwB,KAAK,EAAEC,MAAM,CAACY,WAAY;MAC1BC,WAAW,EAAC,wBAAwB;MACpCC,KAAK,EAAE3B,WAAY;MACnB4B,YAAY,EAAE3B;IAAe,CAC9B,CAAC,EAEFR,IAAA,CAACH,QAAQ;MACPuC,IAAI,EAAE1B,eAAgB;MACtB2B,UAAU,EAAErB,oBAAqB;MACjCsB,YAAY,EAAE,SAAdA,YAAYA,CAAEpB,IAAI;QAAA,OAAIA,IAAI,CAACP,EAAE;MAAA,CAAC;MAC9B4B,qBAAqB,EAAEnB,MAAM,CAACoB;IAAc,CAC7C,CAAC;EAAA,CACE,CAAC;AAEX,CAAC;AAED,IAAMpB,MAAM,GAAG1B,UAAU,CAAC+C,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDX,WAAW,EAAE;IACXW,eAAe,EAAE,OAAO;IACxBC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDR,aAAa,EAAE;IACbI,OAAO,EAAE;EACX,CAAC;EACDvB,WAAW,EAAE;IACXsB,eAAe,EAAE,OAAO;IACxBC,OAAO,EAAE,EAAE;IACXK,YAAY,EAAE,EAAE;IAChBH,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDtB,WAAW,EAAE;IACXwB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBF,YAAY,EAAE;EAChB,CAAC;EACDtB,cAAc,EAAE;IACduB,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,MAAM;IACbH,YAAY,EAAE;EAChB,CAAC;EACDrB,WAAW,EAAE;IACXyB,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDzB,eAAe,EAAE;IACfqB,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE;EACT,CAAC;EACDtB,YAAY,EAAE;IACZoB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAEF,eAAejD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}