{"ast": null, "code": "export { createRoot } from 'react-dom/client';", "map": {"version": 3, "names": ["createRoot"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\expo\\src\\launch\\createRoot.tsx"], "sourcesContent": ["export { createRoot } from 'react-dom/client';\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}