{"ast": null, "code": "'use client';\n\nimport Animated from \"../../vendor/react-native/Animated/Animated\";\nexport default Animated;", "map": {"version": 3, "names": ["Animated"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/exports/Animated/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport Animated from '../../vendor/react-native/Animated/Animated';\nexport default Animated;"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,QAAQ;AACf,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}