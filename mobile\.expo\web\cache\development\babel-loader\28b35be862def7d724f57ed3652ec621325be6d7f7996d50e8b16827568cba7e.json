{"ast": null, "code": "import { addEventListener } from \"../addEventListener\";\nimport useLayoutEffect from \"../useLayoutEffect\";\nimport useStable from \"../useStable\";\nexport default function useEvent(eventType, options) {\n  var targetListeners = useStable(function () {\n    return new Map();\n  });\n  var addListener = useStable(function () {\n    return function (target, callback) {\n      var removeTargetListener = targetListeners.get(target);\n      if (removeTargetListener != null) {\n        removeTargetListener();\n      }\n      if (callback == null) {\n        targetListeners.delete(target);\n        callback = function callback() {};\n      }\n      var removeEventListener = addEventListener(target, eventType, callback, options);\n      targetListeners.set(target, removeEventListener);\n      return removeEventListener;\n    };\n  });\n  useLayoutEffect(function () {\n    return function () {\n      targetListeners.forEach(function (removeListener) {\n        removeListener();\n      });\n      targetListeners.clear();\n    };\n  }, [targetListeners]);\n  return addListener;\n}", "map": {"version": 3, "names": ["addEventListener", "useLayoutEffect", "useStable", "useEvent", "eventType", "options", "targetListeners", "Map", "addListener", "target", "callback", "removeTargetListener", "get", "delete", "removeEventListener", "set", "for<PERSON>ach", "removeListener", "clear"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/modules/useEvent/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { addEventListener } from '../addEventListener';\nimport useLayoutEffect from '../useLayoutEffect';\nimport useStable from '../useStable';\n/**\n * This can be used with any event type include custom events.\n *\n * const click = useEvent('click', options);\n * useEffect(() => {\n *   click.setListener(target, onClick);\n *   return () => click.clear();\n * }).\n */\nexport default function useEvent(eventType, options) {\n  var targetListeners = useStable(() => new Map());\n  var addListener = useStable(() => {\n    return (target, callback) => {\n      var removeTargetListener = targetListeners.get(target);\n      if (removeTargetListener != null) {\n        removeTargetListener();\n      }\n      if (callback == null) {\n        targetListeners.delete(target);\n        callback = () => {};\n      }\n      var removeEventListener = addEventListener(target, eventType, callback, options);\n      targetListeners.set(target, removeEventListener);\n      return removeEventListener;\n    };\n  });\n  useLayoutEffect(() => {\n    return () => {\n      targetListeners.forEach(removeListener => {\n        removeListener();\n      });\n      targetListeners.clear();\n    };\n  }, [targetListeners]);\n  return addListener;\n}"], "mappings": "AASA,SAASA,gBAAgB;AACzB,OAAOC,eAAe;AACtB,OAAOC,SAAS;AAUhB,eAAe,SAASC,QAAQA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACnD,IAAIC,eAAe,GAAGJ,SAAS,CAAC;IAAA,OAAM,IAAIK,GAAG,CAAC,CAAC;EAAA,EAAC;EAChD,IAAIC,WAAW,GAAGN,SAAS,CAAC,YAAM;IAChC,OAAO,UAACO,MAAM,EAAEC,QAAQ,EAAK;MAC3B,IAAIC,oBAAoB,GAAGL,eAAe,CAACM,GAAG,CAACH,MAAM,CAAC;MACtD,IAAIE,oBAAoB,IAAI,IAAI,EAAE;QAChCA,oBAAoB,CAAC,CAAC;MACxB;MACA,IAAID,QAAQ,IAAI,IAAI,EAAE;QACpBJ,eAAe,CAACO,MAAM,CAACJ,MAAM,CAAC;QAC9BC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS,CAAC,CAAC;MACrB;MACA,IAAII,mBAAmB,GAAGd,gBAAgB,CAACS,MAAM,EAAEL,SAAS,EAAEM,QAAQ,EAAEL,OAAO,CAAC;MAChFC,eAAe,CAACS,GAAG,CAACN,MAAM,EAAEK,mBAAmB,CAAC;MAChD,OAAOA,mBAAmB;IAC5B,CAAC;EACH,CAAC,CAAC;EACFb,eAAe,CAAC,YAAM;IACpB,OAAO,YAAM;MACXK,eAAe,CAACU,OAAO,CAAC,UAAAC,cAAc,EAAI;QACxCA,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC;MACFX,eAAe,CAACY,KAAK,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACZ,eAAe,CAAC,CAAC;EACrB,OAAOE,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}