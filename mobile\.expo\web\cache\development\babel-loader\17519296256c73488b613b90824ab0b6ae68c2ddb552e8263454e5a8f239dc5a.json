{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { UnavailabilityError } from 'expo-modules-core';\nimport { useEffect } from 'react';\nimport ExpoKeepAwake from \"./ExpoKeepAwake\";\nexport var ExpoKeepAwakeTag = 'ExpoKeepAwakeDefaultTag';\nexport function isAvailableAsync() {\n  return _isAvailableAsync.apply(this, arguments);\n}\nfunction _isAvailableAsync() {\n  _isAvailableAsync = _asyncToGenerator(function* () {\n    if (ExpoKeepAwake.isAvailableAsync) {\n      return yield ExpoKeepAwake.isAvailableAsync();\n    }\n    return true;\n  });\n  return _isAvailableAsync.apply(this, arguments);\n}\nexport function useKeepAwake() {\n  var tag = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ExpoKeepAwakeTag;\n  var options = arguments.length > 1 ? arguments[1] : undefined;\n  useEffect(function () {\n    var isMounted = true;\n    activateKeepAwakeAsync(tag).then(function () {\n      if (isMounted && ExpoKeepAwake.addListenerForTag && options != null && options.listener) {\n        addListener(tag, options.listener);\n      }\n    });\n    return function () {\n      isMounted = false;\n      if (options != null && options.suppressDeactivateWarnings) {\n        deactivateKeepAwake(tag).catch(function () {});\n      } else {\n        deactivateKeepAwake(tag);\n      }\n    };\n  }, [tag]);\n}\nexport function activateKeepAwake() {\n  var tag = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ExpoKeepAwakeTag;\n  console.warn('`activateKeepAwake` is deprecated. Use `activateKeepAwakeAsync` instead.');\n  return activateKeepAwakeAsync(tag);\n}\nexport function activateKeepAwakeAsync() {\n  return _activateKeepAwakeAsync.apply(this, arguments);\n}\nfunction _activateKeepAwakeAsync() {\n  _activateKeepAwakeAsync = _asyncToGenerator(function* () {\n    var tag = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ExpoKeepAwakeTag;\n    yield ExpoKeepAwake.activate == null ? void 0 : ExpoKeepAwake.activate(tag);\n  });\n  return _activateKeepAwakeAsync.apply(this, arguments);\n}\nexport function deactivateKeepAwake() {\n  return _deactivateKeepAwake.apply(this, arguments);\n}\nfunction _deactivateKeepAwake() {\n  _deactivateKeepAwake = _asyncToGenerator(function* () {\n    var tag = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ExpoKeepAwakeTag;\n    yield ExpoKeepAwake.deactivate == null ? void 0 : ExpoKeepAwake.deactivate(tag);\n  });\n  return _deactivateKeepAwake.apply(this, arguments);\n}\nexport function addListener(tagOrListener, listener) {\n  if (!ExpoKeepAwake.addListenerForTag) {\n    throw new UnavailabilityError('ExpoKeepAwake', 'addListenerForTag');\n  }\n  var tag = typeof tagOrListener === 'string' ? tagOrListener : ExpoKeepAwakeTag;\n  var _listener = typeof tagOrListener === 'function' ? tagOrListener : listener;\n  return ExpoKeepAwake.addListenerForTag(tag, _listener);\n}\nexport * from \"./KeepAwake.types\";", "map": {"version": 3, "names": ["UnavailabilityError", "useEffect", "ExpoKeepAwake", "ExpoKeepAwakeTag", "isAvailableAsync", "_isAvailableAsync", "apply", "arguments", "_asyncToGenerator", "useKeepAwake", "tag", "length", "undefined", "options", "isMounted", "activateKeepAwakeAsync", "then", "addListenerForTag", "listener", "addListener", "suppressDeactivateWarnings", "deactivateKeepAwake", "catch", "activateKeepAwake", "console", "warn", "_activateKeepAwakeAsync", "activate", "_deactivateKeepAwake", "deactivate", "tagOrListener", "_listener"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\expo-keep-awake\\src\\index.ts"], "sourcesContent": ["import { Subscription, UnavailabilityError } from 'expo-modules-core';\nimport { useEffect } from 'react';\n\nimport ExpoKeepAwake from './ExpoKeepAwake';\nimport { KeepAwakeListener, KeepAwakeOptions } from './KeepAwake.types';\n\n/** Default tag, used when no tag has been specified in keep awake method calls. */\nexport const ExpoKeepAwakeTag = 'ExpoKeepAwakeDefaultTag';\n\n/** @returns `true` on all platforms except [unsupported web browsers](https://caniuse.com/wake-lock). */\nexport async function isAvailableAsync(): Promise<boolean> {\n  if (ExpoKeepAwake.isAvailableAsync) {\n    return await ExpoKeepAwake.isAvailableAsync();\n  }\n  return true;\n}\n\n/**\n * A React hook to keep the screen awake for as long as the owner component is mounted.\n * The optionally provided `tag` argument is used when activating and deactivating the keep-awake\n * feature. If unspecified, the default `tag` is used. See the documentation for `activateKeepAwakeAsync`\n * below to learn more about the `tag` argument.\n *\n * @param tag Tag to lock screen sleep prevention. If not provided, the default tag is used.\n * @param options Additional options for the keep awake hook.\n */\nexport function useKeepAwake(tag: string = ExpoKeepAwakeTag, options?: KeepAwakeOptions): void {\n  useEffect(() => {\n    let isMounted = true;\n    activateKeepAwakeAsync(tag).then(() => {\n      if (isMounted && ExpoKeepAwake.addListenerForTag && options?.listener) {\n        addListener(tag, options.listener);\n      }\n    });\n    return () => {\n      isMounted = false;\n      if (options?.suppressDeactivateWarnings) {\n        deactivateKeepAwake(tag).catch(() => {});\n      } else {\n        deactivateKeepAwake(tag);\n      }\n    };\n  }, [tag]);\n}\n\n// @needsAudit\n/**\n * Prevents the screen from sleeping until `deactivateKeepAwake` is called with the same `tag` value.\n *\n * If the `tag` argument is specified, the screen will not sleep until you call `deactivateKeepAwake`\n * with the same `tag` argument. When using multiple `tags` for activation you'll have to deactivate\n * each one in order to re-enable screen sleep. If tag is unspecified, the default `tag` is used.\n *\n * Web support [is limited](https://caniuse.com/wake-lock).\n *\n * @param tag Tag to lock screen sleep prevention. If not provided, the default tag is used.\n * @deprecated use `activateKeepAwakeAsync` instead.\n */\nexport function activateKeepAwake(tag: string = ExpoKeepAwakeTag): Promise<void> {\n  console.warn('`activateKeepAwake` is deprecated. Use `activateKeepAwakeAsync` instead.');\n  return activateKeepAwakeAsync(tag);\n}\n\n// @needsAudit\n/**\n * Prevents the screen from sleeping until `deactivateKeepAwake` is called with the same `tag` value.\n *\n * If the `tag` argument is specified, the screen will not sleep until you call `deactivateKeepAwake`\n * with the same `tag` argument. When using multiple `tags` for activation you'll have to deactivate\n * each one in order to re-enable screen sleep. If tag is unspecified, the default `tag` is used.\n *\n * Web support [is limited](https://caniuse.com/wake-lock).\n *\n * @param tag Tag to lock screen sleep prevention. If not provided, the default tag is used.\n */\nexport async function activateKeepAwakeAsync(tag: string = ExpoKeepAwakeTag): Promise<void> {\n  await ExpoKeepAwake.activate?.(tag);\n}\n\n// @needsAudit\n/**\n * Releases the lock on screen-sleep prevention associated with the given `tag` value. If `tag`\n * is unspecified, it defaults to the same default tag that `activateKeepAwake` uses.\n *\n * @param tag Tag to release the lock on screen sleep prevention. If not provided,\n * the default tag is used.\n */\nexport async function deactivateKeepAwake(tag: string = ExpoKeepAwakeTag): Promise<void> {\n  await ExpoKeepAwake.deactivate?.(tag);\n}\n\n/**\n * Observe changes to the keep awake timer.\n * On web, this changes when navigating away from the active window/tab. No-op on native.\n * @platform web\n *\n * @example\n * ```ts\n * KeepAwake.addListener(({ state }) => {\n *   // ...\n * });\n * ```\n */\nexport function addListener(\n  tagOrListener: string | KeepAwakeListener,\n  listener?: KeepAwakeListener\n): Subscription {\n  // Assert so the type is non-nullable.\n  if (!ExpoKeepAwake.addListenerForTag) {\n    throw new UnavailabilityError('ExpoKeepAwake', 'addListenerForTag');\n  }\n\n  const tag = typeof tagOrListener === 'string' ? tagOrListener : ExpoKeepAwakeTag;\n  const _listener = typeof tagOrListener === 'function' ? tagOrListener : listener;\n\n  return ExpoKeepAwake.addListenerForTag(tag, _listener);\n}\n\nexport * from './KeepAwake.types';\n"], "mappings": ";AAAA,SAAuBA,mBAAmB,QAAQ,mBAAmB;AACrE,SAASC,SAAS,QAAQ,OAAO;AAEjC,OAAOC,aAAa;AAIpB,OAAO,IAAMC,gBAAgB,GAAG,yBAAyB;AAGzD,gBAAsBC,gBAAgBA,CAAA;EAAA,OAAAC,iBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAKrC,SAAAF,kBAAA;EAAAA,iBAAA,GAAAG,iBAAA,CALM,aAA+B;IACpC,IAAIN,aAAa,CAACE,gBAAgB,EAAE;MAClC,aAAaF,aAAa,CAACE,gBAAgB,EAAE;;IAE/C,OAAO,IAAI;EACb,CAAC;EAAA,OAAAC,iBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAWD,OAAM,SAAUE,YAAYA,CAAA,EAA2D;EAAA,IAA1DC,GAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAcJ,gBAAgB;EAAA,IAAEU,OAA0B,GAAAN,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;EACrFX,SAAS,CAAC,YAAK;IACb,IAAIa,SAAS,GAAG,IAAI;IACpBC,sBAAsB,CAACL,GAAG,CAAC,CAACM,IAAI,CAAC,YAAK;MACpC,IAAIF,SAAS,IAAIZ,aAAa,CAACe,iBAAiB,IAAIJ,OAAO,YAAPA,OAAO,CAAEK,QAAQ,EAAE;QACrEC,WAAW,CAACT,GAAG,EAAEG,OAAO,CAACK,QAAQ,CAAC;;IAEtC,CAAC,CAAC;IACF,OAAO,YAAK;MACVJ,SAAS,GAAG,KAAK;MACjB,IAAID,OAAO,YAAPA,OAAO,CAAEO,0BAA0B,EAAE;QACvCC,mBAAmB,CAACX,GAAG,CAAC,CAACY,KAAK,CAAC,YAAK,CAAE,CAAC,CAAC;OACzC,MAAM;QACLD,mBAAmB,CAACX,GAAG,CAAC;;IAE5B,CAAC;EACH,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;AACX;AAeA,OAAM,SAAUa,iBAAiBA,CAAA,EAA+B;EAAA,IAA9Bb,GAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAcJ,gBAAgB;EAC9DqB,OAAO,CAACC,IAAI,CAAC,0EAA0E,CAAC;EACxF,OAAOV,sBAAsB,CAACL,GAAG,CAAC;AACpC;AAcA,gBAAsBK,sBAAsBA,CAAA;EAAA,OAAAW,uBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAE3C,SAAAmB,wBAAA;EAAAA,uBAAA,GAAAlB,iBAAA,CAFM,aAAoE;IAAA,IAA9BE,GAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAcJ,gBAAgB;IACzE,MAAMD,aAAa,CAACyB,QAAQ,oBAAtBzB,aAAa,CAACyB,QAAQ,CAAGjB,GAAG,CAAC;EACrC,CAAC;EAAA,OAAAgB,uBAAA,CAAApB,KAAA,OAAAC,SAAA;AAAA;AAUD,gBAAsBc,mBAAmBA,CAAA;EAAA,OAAAO,oBAAA,CAAAtB,KAAA,OAAAC,SAAA;AAAA;AAExC,SAAAqB,qBAAA;EAAAA,oBAAA,GAAApB,iBAAA,CAFM,aAAiE;IAAA,IAA9BE,GAAA,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAcJ,gBAAgB;IACtE,MAAMD,aAAa,CAAC2B,UAAU,oBAAxB3B,aAAa,CAAC2B,UAAU,CAAGnB,GAAG,CAAC;EACvC,CAAC;EAAA,OAAAkB,oBAAA,CAAAtB,KAAA,OAAAC,SAAA;AAAA;AAcD,OAAM,SAAUY,WAAWA,CACzBW,aAAyC,EACzCZ,QAA4B;EAG5B,IAAI,CAAChB,aAAa,CAACe,iBAAiB,EAAE;IACpC,MAAM,IAAIjB,mBAAmB,CAAC,eAAe,EAAE,mBAAmB,CAAC;;EAGrE,IAAMU,GAAG,GAAG,OAAOoB,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG3B,gBAAgB;EAChF,IAAM4B,SAAS,GAAG,OAAOD,aAAa,KAAK,UAAU,GAAGA,aAAa,GAAGZ,QAAQ;EAEhF,OAAOhB,aAAa,CAACe,iBAAiB,CAACP,GAAG,EAAEqB,SAAS,CAAC;AACxD;AAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}