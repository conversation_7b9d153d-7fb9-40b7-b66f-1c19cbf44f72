{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { nanoid } from 'nanoid/non-secure';\nvar BaseRouter = {\n  getStateForAction: function getStateForAction(state, action) {\n    switch (action.type) {\n      case 'SET_PARAMS':\n        {\n          var index = action.source ? state.routes.findIndex(function (r) {\n            return r.key === action.source;\n          }) : state.index;\n          if (index === -1) {\n            return null;\n          }\n          return _objectSpread(_objectSpread({}, state), {}, {\n            routes: state.routes.map(function (r, i) {\n              return i === index ? _objectSpread(_objectSpread({}, r), {}, {\n                params: _objectSpread(_objectSpread({}, r.params), action.payload.params)\n              }) : r;\n            })\n          });\n        }\n      case 'RESET':\n        {\n          var nextState = action.payload;\n          if (nextState.routes.length === 0 || nextState.routes.some(function (route) {\n            return !state.routeNames.includes(route.name);\n          })) {\n            return null;\n          }\n          if (nextState.stale === false) {\n            if (state.routeNames.length !== nextState.routeNames.length || nextState.routeNames.some(function (name) {\n              return !state.routeNames.includes(name);\n            })) {\n              return null;\n            }\n            return _objectSpread(_objectSpread({}, nextState), {}, {\n              routes: nextState.routes.map(function (route) {\n                return route.key ? route : _objectSpread(_objectSpread({}, route), {}, {\n                  key: `${route.name}-${nanoid()}`\n                });\n              })\n            });\n          }\n          return nextState;\n        }\n      default:\n        return null;\n    }\n  },\n  shouldActionChangeFocus: function shouldActionChangeFocus(action) {\n    return action.type === 'NAVIGATE';\n  }\n};\nexport default BaseRouter;", "map": {"version": 3, "names": ["nanoid", "BaseRouter", "getStateForAction", "state", "action", "type", "index", "source", "routes", "findIndex", "r", "key", "_objectSpread", "map", "i", "params", "payload", "nextState", "length", "some", "route", "routeNames", "includes", "name", "stale", "shouldActionChangeFocus"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\routers\\src\\BaseRouter.tsx"], "sourcesContent": ["import { nanoid } from 'nanoid/non-secure';\n\nimport type {\n  CommonNavigationAction,\n  NavigationState,\n  PartialState,\n} from './types';\n\n/**\n * Base router object that can be used when writing custom routers.\n * This provides few helper methods to handle common actions such as `RESET`.\n */\nconst BaseRouter = {\n  getStateForAction<State extends NavigationState>(\n    state: State,\n    action: CommonNavigationAction\n  ): State | PartialState<State> | null {\n    switch (action.type) {\n      case 'SET_PARAMS': {\n        const index = action.source\n          ? state.routes.findIndex((r) => r.key === action.source)\n          : state.index;\n\n        if (index === -1) {\n          return null;\n        }\n\n        return {\n          ...state,\n          routes: state.routes.map((r, i) =>\n            i === index\n              ? { ...r, params: { ...r.params, ...action.payload.params } }\n              : r\n          ),\n        };\n      }\n\n      case 'RESET': {\n        const nextState = action.payload as State | PartialState<State>;\n\n        if (\n          nextState.routes.length === 0 ||\n          nextState.routes.some(\n            (route: { name: string }) => !state.routeNames.includes(route.name)\n          )\n        ) {\n          return null;\n        }\n\n        if (nextState.stale === false) {\n          if (\n            state.routeNames.length !== nextState.routeNames.length ||\n            nextState.routeNames.some(\n              (name) => !state.routeNames.includes(name)\n            )\n          ) {\n            return null;\n          }\n\n          return {\n            ...nextState,\n            routes: nextState.routes.map((route) =>\n              route.key ? route : { ...route, key: `${route.name}-${nanoid()}` }\n            ),\n          };\n        }\n\n        return nextState;\n      }\n\n      default:\n        return null;\n    }\n  },\n\n  shouldActionChangeFocus(action: CommonNavigationAction) {\n    return action.type === 'NAVIGATE';\n  },\n};\n\nexport default BaseRouter;\n"], "mappings": ";;;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAY1C,IAAMC,UAAU,GAAG;EACjBC,iBAAiB,WAAjBA,iBAAiBA,CACfC,KAAY,EACZC,MAA8B,EACM;IACpC,QAAQA,MAAM,CAACC,IAAI;MACjB,KAAK,YAAY;QAAE;UACjB,IAAMC,KAAK,GAAGF,MAAM,CAACG,MAAM,GACvBJ,KAAK,CAACK,MAAM,CAACC,SAAS,CAAE,UAAAC,CAAC;YAAA,OAAKA,CAAC,CAACC,GAAG,KAAKP,MAAM,CAACG,MAAM;UAAA,EAAC,GACtDJ,KAAK,CAACG,KAAK;UAEf,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO,IAAI;UACb;UAEA,OAAAM,aAAA,CAAAA,aAAA,KACKT,KAAK;YACRK,MAAM,EAAEL,KAAK,CAACK,MAAM,CAACK,GAAG,CAAC,UAACH,CAAC,EAAEI,CAAC;cAAA,OAC5BA,CAAC,KAAKR,KAAK,GAAAM,aAAA,CAAAA,aAAA,KACFF,CAAC;gBAAEK,MAAM,EAAAH,aAAA,CAAAA,aAAA,KAAOF,CAAC,CAACK,MAAM,GAAKX,MAAM,CAACY,OAAO,CAACD,MAAA;cAAO,KACxDL,CAAC;YAAA;UAAA;QAGX;MAEA,KAAK,OAAO;QAAE;UACZ,IAAMO,SAAS,GAAGb,MAAM,CAACY,OAAsC;UAE/D,IACEC,SAAS,CAACT,MAAM,CAACU,MAAM,KAAK,CAAC,IAC7BD,SAAS,CAACT,MAAM,CAACW,IAAI,CAClB,UAAAC,KAAuB;YAAA,OAAK,CAACjB,KAAK,CAACkB,UAAU,CAACC,QAAQ,CAACF,KAAK,CAACG,IAAI,CAAC;UAAA,EACpE,EACD;YACA,OAAO,IAAI;UACb;UAEA,IAAIN,SAAS,CAACO,KAAK,KAAK,KAAK,EAAE;YAC7B,IACErB,KAAK,CAACkB,UAAU,CAACH,MAAM,KAAKD,SAAS,CAACI,UAAU,CAACH,MAAM,IACvDD,SAAS,CAACI,UAAU,CAACF,IAAI,CACtB,UAAAI,IAAI;cAAA,OAAK,CAACpB,KAAK,CAACkB,UAAU,CAACC,QAAQ,CAACC,IAAI,CAAC;YAAA,EAC3C,EACD;cACA,OAAO,IAAI;YACb;YAEA,OAAAX,aAAA,CAAAA,aAAA,KACKK,SAAS;cACZT,MAAM,EAAES,SAAS,CAACT,MAAM,CAACK,GAAG,CAAE,UAAAO,KAAK;gBAAA,OACjCA,KAAK,CAACT,GAAG,GAAGS,KAAK,GAAAR,aAAA,CAAAA,aAAA,KAAQQ,KAAK;kBAAET,GAAG,EAAG,GAAES,KAAK,CAACG,IAAK,IAAGvB,MAAM,EAAG;gBAAA,EAAG;cAAA;YAAA;UAGxE;UAEA,OAAOiB,SAAS;QAClB;MAEA;QACE,OAAO,IAAI;IAAC;EAElB,CAAC;EAEDQ,uBAAuB,WAAvBA,uBAAuBA,CAACrB,MAA8B,EAAE;IACtD,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU;EACnC;AACF,CAAC;AAED,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}