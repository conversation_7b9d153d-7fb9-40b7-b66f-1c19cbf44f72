{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"onPressIn\", \"onPressOut\", \"android_ripple\", \"pressColor\", \"pressOpacity\", \"style\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport Easing from \"react-native-web/dist/exports/Easing\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport Pressable from \"react-native-web/dist/exports/Pressable\";\nvar AnimatedPressable = Animated.createAnimatedComponent(Pressable);\nvar ANDROID_VERSION_LOLLIPOP = 21;\nvar ANDROID_SUPPORTS_RIPPLE = Platform.OS === 'android' && Platform.Version >= ANDROID_VERSION_LOLLIPOP;\nexport default function PlatformPressable(_ref) {\n  var onPressIn = _ref.onPressIn,\n    onPressOut = _ref.onPressOut,\n    android_ripple = _ref.android_ripple,\n    pressColor = _ref.pressColor,\n    _ref$pressOpacity = _ref.pressOpacity,\n    pressOpacity = _ref$pressOpacity === void 0 ? 0.3 : _ref$pressOpacity,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useTheme = useTheme(),\n    dark = _useTheme.dark;\n  var _React$useState = React.useState(function () {\n      return new Animated.Value(1);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    opacity = _React$useState2[0];\n  var animateTo = function animateTo(toValue, duration) {\n    if (ANDROID_SUPPORTS_RIPPLE) {\n      return;\n    }\n    Animated.timing(opacity, {\n      toValue: toValue,\n      duration: duration,\n      easing: Easing.inOut(Easing.quad),\n      useNativeDriver: true\n    }).start();\n  };\n  var handlePressIn = function handlePressIn(e) {\n    animateTo(pressOpacity, 0);\n    onPressIn === null || onPressIn === void 0 ? void 0 : onPressIn(e);\n  };\n  var handlePressOut = function handlePressOut(e) {\n    animateTo(1, 200);\n    onPressOut === null || onPressOut === void 0 ? void 0 : onPressOut(e);\n  };\n  return React.createElement(AnimatedPressable, _extends({\n    onPressIn: handlePressIn,\n    onPressOut: handlePressOut,\n    android_ripple: ANDROID_SUPPORTS_RIPPLE ? _objectSpread({\n      color: pressColor !== undefined ? pressColor : dark ? 'rgba(255, 255, 255, .32)' : 'rgba(0, 0, 0, .32)'\n    }, android_ripple) : undefined,\n    style: [{\n      opacity: !ANDROID_SUPPORTS_RIPPLE ? opacity : 1\n    }, style]\n  }, rest));\n}", "map": {"version": 3, "names": ["useTheme", "React", "Animated", "Easing", "Platform", "Pressable", "AnimatedPressable", "createAnimatedComponent", "ANDROID_VERSION_LOLLIPOP", "ANDROID_SUPPORTS_RIPPLE", "OS", "Version", "PlatformPressable", "_ref", "onPressIn", "onPressOut", "android_ripple", "pressColor", "_ref$pressOpacity", "pressOpacity", "style", "rest", "_objectWithoutProperties", "_excluded", "_useTheme", "dark", "_React$useState", "useState", "Value", "_React$useState2", "_slicedToArray", "opacity", "animateTo", "toValue", "duration", "timing", "easing", "inOut", "quad", "useNativeDriver", "start", "handlePressIn", "e", "handlePressOut", "createElement", "_extends", "_objectSpread", "color", "undefined"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\elements\\src\\PlatformPressable.tsx"], "sourcesContent": ["import { useTheme } from '@react-navigation/native';\nimport * as React from 'react';\nimport {\n  Animated,\n  Easing,\n  GestureResponderEvent,\n  Platform,\n  Pressable,\n  PressableProps,\n  StyleProp,\n  ViewStyle,\n} from 'react-native';\n\nexport type Props = Omit<PressableProps, 'style'> & {\n  pressColor?: string;\n  pressOpacity?: number;\n  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;\n  children: React.ReactNode;\n};\n\nconst AnimatedPressable = Animated.createAnimatedComponent(Pressable);\n\nconst ANDROID_VERSION_LOLLIPOP = 21;\nconst ANDROID_SUPPORTS_RIPPLE =\n  Platform.OS === 'android' && Platform.Version >= ANDROID_VERSION_LOLLIPOP;\n\n/**\n * PlatformPressable provides an abstraction on top of Pressable to handle platform differences.\n */\nexport default function PlatformPressable({\n  onPressIn,\n  onPressOut,\n  android_ripple,\n  pressColor,\n  pressOpacity = 0.3,\n  style,\n  ...rest\n}: Props) {\n  const { dark } = useTheme();\n  const [opacity] = React.useState(() => new Animated.Value(1));\n\n  const animateTo = (toValue: number, duration: number) => {\n    if (ANDROID_SUPPORTS_RIPPLE) {\n      return;\n    }\n\n    Animated.timing(opacity, {\n      toValue,\n      duration,\n      easing: Easing.inOut(Easing.quad),\n      useNativeDriver: true,\n    }).start();\n  };\n\n  const handlePressIn = (e: GestureResponderEvent) => {\n    animateTo(pressOpacity, 0);\n    onPressIn?.(e);\n  };\n\n  const handlePressOut = (e: GestureResponderEvent) => {\n    animateTo(1, 200);\n    onPressOut?.(e);\n  };\n\n  return (\n    <AnimatedPressable\n      onPressIn={handlePressIn}\n      onPressOut={handlePressOut}\n      android_ripple={\n        ANDROID_SUPPORTS_RIPPLE\n          ? {\n              color:\n                pressColor !== undefined\n                  ? pressColor\n                  : dark\n                  ? 'rgba(255, 255, 255, .32)'\n                  : 'rgba(0, 0, 0, .32)',\n              ...android_ripple,\n            }\n          : undefined\n      }\n      style={[{ opacity: !ANDROID_SUPPORTS_RIPPLE ? opacity : 1 }, style]}\n      {...rest}\n    />\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,MAAA;AAAA,OAAAC,QAAA;AAAA,OAAAC,SAAA;AAmB9B,IAAMC,iBAAiB,GAAGJ,QAAQ,CAACK,uBAAuB,CAACF,SAAS,CAAC;AAErE,IAAMG,wBAAwB,GAAG,EAAE;AACnC,IAAMC,uBAAuB,GAC3BL,QAAQ,CAACM,EAAE,KAAK,SAAS,IAAIN,QAAQ,CAACO,OAAO,IAAIH,wBAAwB;AAK3E,eAAe,SAASI,iBAAiBA,CAAAC,IAAA,EAQ/B;EAAA,IAPRC,SAAS,GAOHD,IAAA,CAPNC,SAAS;IACTC,UAAU,GAMJF,IAAA,CANNE,UAAU;IACVC,cAAc,GAKRH,IAAA,CALNG,cAAc;IACdC,UAAU,GAIJJ,IAAA,CAJNI,UAAU;IAAAC,iBAAA,GAIJL,IAAA,CAHNM,YAAY;IAAZA,YAAY,GAAAD,iBAAA,cAAG,GAAG,GAAAA,iBAAA;IAClBE,KAAK,GAECP,IAAA,CAFNO,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGT,IAAA,EAAAU,SAAA;EACN,IAAAC,SAAA,GAAiBxB,QAAQ,EAAE;IAAnByB,IAAA,GAAAD,SAAA,CAAAC,IAAA;EACR,IAAAC,eAAA,GAAkBzB,KAAK,CAAC0B,QAAQ,CAAC;MAAA,OAAM,IAAIzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,CAAC;IAAA,EAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAAtDK,OAAO,GAAAF,gBAAA;EAEd,IAAMG,SAAS,GAAG,SAAZA,SAASA,CAAIC,OAAe,EAAEC,QAAgB,EAAK;IACvD,IAAIzB,uBAAuB,EAAE;MAC3B;IACF;IAEAP,QAAQ,CAACiC,MAAM,CAACJ,OAAO,EAAE;MACvBE,OAAO,EAAPA,OAAO;MACPC,QAAQ,EAARA,QAAQ;MACRE,MAAM,EAAEjC,MAAM,CAACkC,KAAK,CAAClC,MAAM,CAACmC,IAAI,CAAC;MACjCC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,EAAE;EACZ,CAAC;EAED,IAAMC,aAAa,GAAI,SAAjBA,aAAaA,CAAIC,CAAwB,EAAK;IAClDV,SAAS,CAACb,YAAY,EAAE,CAAC,CAAC;IAC1BL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAG4B,CAAC,CAAC;EAChB,CAAC;EAED,IAAMC,cAAc,GAAI,SAAlBA,cAAcA,CAAID,CAAwB,EAAK;IACnDV,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;IACjBjB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG2B,CAAC,CAAC;EACjB,CAAC;EAED,OACEzC,KAAA,CAAA2C,aAAA,CAACtC,iBAAiB,EAAAuC,QAAA;IAChB/B,SAAS,EAAE2B,aAAc;IACzB1B,UAAU,EAAE4B,cAAe;IAC3B3B,cAAc,EACZP,uBAAuB,GAAAqC,aAAA;MAEjBC,KAAK,EACH9B,UAAU,KAAK+B,SAAS,GACpB/B,UAAU,GACVQ,IAAI,GACJ,0BAA0B,GAC1B;IAAoB,GACvBT,cAAA,IAELgC,SACL;IACD5B,KAAK,EAAE,CAAC;MAAEW,OAAO,EAAE,CAACtB,uBAAuB,GAAGsB,OAAO,GAAG;IAAE,CAAC,EAAEX,KAAK;EAAE,GAChEC,IAAI,EACR;AAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}