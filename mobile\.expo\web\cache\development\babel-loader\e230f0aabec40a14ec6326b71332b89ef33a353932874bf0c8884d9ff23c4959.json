{"ast": null, "code": "import * as React from 'react';\nexport default function useKeyedChildListeners() {\n  var _React$useRef = React.useRef(Object.assign(Object.create(null), {\n      getState: {},\n      beforeRemove: {}\n    })),\n    keyedListeners = _React$useRef.current;\n  var addKeyedListener = React.useCallback(function (type, key, listener) {\n    keyedListeners[type][key] = listener;\n    return function () {\n      keyedListeners[type][key] = undefined;\n    };\n  }, [keyedListeners]);\n  return {\n    keyedListeners: keyedListeners,\n    addKeyedListener: addKeyedListener\n  };\n}", "map": {"version": 3, "names": ["React", "useKeyedChildListeners", "_React$useRef", "useRef", "Object", "assign", "create", "getState", "beforeRemove", "keyedListeners", "current", "addKeyedListener", "useCallback", "type", "key", "listener", "undefined"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\useKeyedChildListeners.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { KeyedListenerMap } from './NavigationBuilderContext';\n\n/**\n * Hook which lets child navigators add getters to be called for obtaining rehydrated state.\n */\nexport default function useKeyedChildListeners() {\n  const { current: keyedListeners } = React.useRef<{\n    [K in keyof KeyedListenerMap]: Record<\n      string,\n      KeyedListenerMap[K] | undefined\n    >;\n  }>(\n    Object.assign(Object.create(null), {\n      getState: {},\n      beforeRemove: {},\n    })\n  );\n\n  const addKeyedListener = React.useCallback(\n    <T extends keyof KeyedListenerMap>(\n      type: T,\n      key: string,\n      listener: KeyedListenerMap[T]\n    ) => {\n      // @ts-expect-error: according to ref stated above you can use `key` to index type\n      keyedListeners[type][key] = listener;\n\n      return () => {\n        // @ts-expect-error: according to ref stated above you can use `key` to index type\n        keyedListeners[type][key] = undefined;\n      };\n    },\n    [keyedListeners]\n  );\n\n  return {\n    keyedListeners,\n    addKeyedListener,\n  };\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAO9B,eAAe,SAASC,sBAAsBA,CAAA,EAAG;EAC/C,IAAAC,aAAA,GAAoCF,KAAK,CAACG,MAAM,CAM9CC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC,EAAE;MACjCC,QAAQ,EAAE,CAAC,CAAC;MACZC,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC,CACH;IAVgBC,cAAA,GAAAP,aAAA,CAATQ,OAAO;EAYf,IAAMC,gBAAgB,GAAGX,KAAK,CAACY,WAAW,CACxC,UACEC,IAAO,EACPC,GAAW,EACXC,QAA6B,EAC1B;IAEHN,cAAc,CAACI,IAAI,CAAC,CAACC,GAAG,CAAC,GAAGC,QAAQ;IAEpC,OAAO,YAAM;MAEXN,cAAc,CAACI,IAAI,CAAC,CAACC,GAAG,CAAC,GAAGE,SAAS;IACvC,CAAC;EACH,CAAC,EACD,CAACP,cAAc,CAAC,CACjB;EAED,OAAO;IACLA,cAAc,EAAdA,cAAc;IACdE,gBAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}