{"ast": null, "code": "import * as React from 'react';\nimport NavigationRouteContext from \"./NavigationRouteContext\";\nexport default function useRoute() {\n  var route = React.useContext(NavigationRouteContext);\n  if (route === undefined) {\n    throw new Error(\"Couldn't find a route object. Is your component inside a screen in a navigator?\");\n  }\n  return route;\n}", "map": {"version": 3, "names": ["React", "NavigationRouteContext", "useRoute", "route", "useContext", "undefined", "Error"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\useRoute.tsx"], "sourcesContent": ["import type { ParamListBase } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationRouteContext from './NavigationRouteContext';\nimport type { RouteProp } from './types';\n\n/**\n * Hook to access the route prop of the parent screen anywhere.\n *\n * @returns Route prop of the parent screen.\n */\nexport default function useRoute<T extends RouteProp<ParamListBase>>(): T {\n  const route = React.useContext(NavigationRouteContext);\n\n  if (route === undefined) {\n    throw new Error(\n      \"Couldn't find a route object. Is your component inside a screen in a navigator?\"\n    );\n  }\n\n  return route as T;\n}\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,sBAAsB;AAQ7B,eAAe,SAASC,QAAQA,CAAA,EAA0C;EACxE,IAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,sBAAsB,CAAC;EAEtD,IAAIE,KAAK,KAAKE,SAAS,EAAE;IACvB,MAAM,IAAIC,KAAK,CACb,iFAAiF,CAClF;EACH;EAEA,OAAOH,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}