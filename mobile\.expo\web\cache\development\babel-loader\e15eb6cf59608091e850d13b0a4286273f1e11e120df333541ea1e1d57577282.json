{"ast": null, "code": "import * as React from 'react';\nimport NavigationBuilderContext from \"./NavigationBuilderContext\";\nexport default function useOnRouteFocus(_ref) {\n  var router = _ref.router,\n    getState = _ref.getState,\n    sourceRouteKey = _ref.key,\n    setState = _ref.setState;\n  var _React$useContext = React.useContext(NavigationBuilderContext),\n    onRouteFocusParent = _React$useContext.onRouteFocus;\n  return React.useCallback(function (key) {\n    var state = getState();\n    var result = router.getStateForRouteFocus(state, key);\n    if (result !== state) {\n      setState(result);\n    }\n    if (onRouteFocusParent !== undefined && sourceRouteKey !== undefined) {\n      onRouteFocusParent(sourceRouteKey);\n    }\n  }, [getState, onRouteFocusParent, router, setState, sourceRouteKey]);\n}", "map": {"version": 3, "names": ["React", "NavigationBuilderContext", "useOnRouteFocus", "_ref", "router", "getState", "sourceRouteKey", "key", "setState", "_React$useContext", "useContext", "onRouteFocusParent", "onRouteFocus", "useCallback", "state", "result", "getStateForRouteFocus", "undefined"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\useOnRouteFocus.tsx"], "sourcesContent": ["import type {\n  NavigationAction,\n  NavigationState,\n  Router,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationBuilderContext from './NavigationBuilderContext';\n\ntype Options<Action extends NavigationAction> = {\n  router: Router<NavigationState, Action>;\n  getState: () => NavigationState;\n  setState: (state: NavigationState) => void;\n  key?: string;\n};\n\n/**\n * Hook to handle focus actions for a route.\n * Focus action needs to be treated specially, coz when a nested route is focused,\n * the parent navigators also needs to be focused.\n */\nexport default function useOnRouteFocus<Action extends NavigationAction>({\n  router,\n  getState,\n  key: sourceRouteKey,\n  setState,\n}: Options<Action>) {\n  const { onRouteFocus: onRouteFocusParent } = React.useContext(\n    NavigationBuilderContext\n  );\n\n  return React.useCallback(\n    (key: string) => {\n      const state = getState();\n      const result = router.getStateForRouteFocus(state, key);\n\n      if (result !== state) {\n        setState(result);\n      }\n\n      if (onRouteFocusParent !== undefined && sourceRouteKey !== undefined) {\n        onRouteFocusParent(sourceRouteKey);\n      }\n    },\n    [getState, onRouteFocusParent, router, setState, sourceRouteKey]\n  );\n}\n"], "mappings": "AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,wBAAwB;AAc/B,eAAe,SAASC,eAAeA,CAAAC,IAAA,EAKnB;EAAA,IAJlBC,MAAM,GAIUD,IAAA,CAJhBC,MAAM;IACNC,QAAQ,GAGQF,IAAA,CAHhBE,QAAQ;IACHC,cAAc,GAEHH,IAAA,CAFhBI,GAAG;IACHC,QAAA,GACgBL,IAAA,CADhBK,QAAA;EAEA,IAAAC,iBAAA,GAA6CT,KAAK,CAACU,UAAU,CAC3DT,wBAAwB,CACzB;IAFqBU,kBAAA,GAAAF,iBAAA,CAAdG,YAAY;EAIpB,OAAOZ,KAAK,CAACa,WAAW,CACrB,UAAAN,GAAW,EAAK;IACf,IAAMO,KAAK,GAAGT,QAAQ,EAAE;IACxB,IAAMU,MAAM,GAAGX,MAAM,CAACY,qBAAqB,CAACF,KAAK,EAAEP,GAAG,CAAC;IAEvD,IAAIQ,MAAM,KAAKD,KAAK,EAAE;MACpBN,QAAQ,CAACO,MAAM,CAAC;IAClB;IAEA,IAAIJ,kBAAkB,KAAKM,SAAS,IAAIX,cAAc,KAAKW,SAAS,EAAE;MACpEN,kBAAkB,CAACL,cAAc,CAAC;IACpC;EACF,CAAC,EACD,CAACD,QAAQ,EAAEM,kBAAkB,EAAEP,MAAM,EAAEI,QAAQ,EAAEF,cAAc,CAAC,CACjE;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}