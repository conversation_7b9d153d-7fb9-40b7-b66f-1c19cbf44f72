{"name": "@react-native-community/cli", "version": "11.3.10", "description": "React Native CLI", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "bin": {"react-native": "build/bin.js"}, "files": ["build", "!*.d.ts", "!*.map", "setup_env.sh"], "engineStrict": true, "engines": {"node": ">=16"}, "jest": {"testEnvironment": "node"}, "dependencies": {"@react-native-community/cli-clean": "11.3.10", "@react-native-community/cli-config": "11.3.10", "@react-native-community/cli-debugger-ui": "11.3.10", "@react-native-community/cli-doctor": "11.3.10", "@react-native-community/cli-hermes": "11.3.10", "@react-native-community/cli-plugin-metro": "11.3.10", "@react-native-community/cli-server-api": "11.3.10", "@react-native-community/cli-tools": "11.3.10", "@react-native-community/cli-types": "11.3.10", "chalk": "^4.1.2", "commander": "^9.4.1", "execa": "^5.0.0", "find-up": "^4.1.0", "fs-extra": "^8.1.0", "graceful-fs": "^4.1.3", "prompts": "^2.4.0", "semver": "^7.5.2"}, "devDependencies": {"@types/fs-extra": "^8.1.0", "@types/graceful-fs": "^4.1.3", "@types/hapi__joi": "^17.1.6", "@types/prompts": "^2.0.9", "@types/semver": "^6.0.2", "deepmerge": "^4.3.0", "slash": "^3.0.0", "snapshot-diff": "^0.7.0"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli"}, "gitHead": "59e4dac7e56fb05f33508ff804c0eac7448c16a8"}