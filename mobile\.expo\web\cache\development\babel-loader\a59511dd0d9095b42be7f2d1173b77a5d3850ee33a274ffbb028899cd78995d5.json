{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport * as queryString from 'query-string';\nimport fromEntries from \"./fromEntries\";\nimport validatePathConfig from \"./validatePathConfig\";\nvar _getActiveRoute = function getActiveRoute(state) {\n  var route = typeof state.index === 'number' ? state.routes[state.index] : state.routes[state.routes.length - 1];\n  if (route.state) {\n    return _getActiveRoute(route.state);\n  }\n  return route;\n};\nexport default function getPathFromState(state, options) {\n  if (state == null) {\n    throw Error(\"Got 'undefined' for the navigation state. You must pass a valid state object.\");\n  }\n  if (options) {\n    validatePathConfig(options);\n  }\n  var configs = options !== null && options !== void 0 && options.screens ? createNormalizedConfigs(options === null || options === void 0 ? void 0 : options.screens) : {};\n  var path = '/';\n  var current = state;\n  var allParams = {};\n  var _loop = function _loop() {\n      var index = typeof current.index === 'number' ? current.index : 0;\n      var route = current.routes[index];\n      var pattern;\n      var focusedParams;\n      var focusedRoute = _getActiveRoute(state);\n      var currentOptions = configs;\n      var nestedRouteNames = [];\n      var hasNext = true;\n      var _loop2 = function _loop2() {\n        pattern = currentOptions[route.name].pattern;\n        nestedRouteNames.push(route.name);\n        if (route.params) {\n          var stringify = (_currentOptions$route = currentOptions[route.name]) === null || _currentOptions$route === void 0 ? void 0 : _currentOptions$route.stringify;\n          var currentParams = fromEntries(Object.entries(route.params).map(function (_ref) {\n            var _ref3 = _slicedToArray(_ref, 2),\n              key = _ref3[0],\n              value = _ref3[1];\n            return [key, stringify !== null && stringify !== void 0 && stringify[key] ? stringify[key](value) : String(value)];\n          }));\n          if (pattern) {\n            Object.assign(allParams, currentParams);\n          }\n          if (focusedRoute === route) {\n            focusedParams = _objectSpread({}, currentParams);\n            (_pattern = pattern) === null || _pattern === void 0 ? void 0 : _pattern.split('/').filter(function (p) {\n              return p.startsWith(':');\n            }).forEach(function (p) {\n              var name = getParamName(p);\n              if (focusedParams) {\n                delete focusedParams[name];\n              }\n            });\n          }\n        }\n        if (!currentOptions[route.name].screens || route.state === undefined) {\n          hasNext = false;\n        } else {\n          index = typeof route.state.index === 'number' ? route.state.index : route.state.routes.length - 1;\n          var nextRoute = route.state.routes[index];\n          var nestedConfig = currentOptions[route.name].screens;\n          if (nestedConfig && nextRoute.name in nestedConfig) {\n            route = nextRoute;\n            currentOptions = nestedConfig;\n          } else {\n            hasNext = false;\n          }\n        }\n      };\n      while (route.name in currentOptions && hasNext) {\n        _loop2();\n      }\n      if (pattern === undefined) {\n        pattern = nestedRouteNames.join('/');\n      }\n      if (currentOptions[route.name] !== undefined) {\n        path += pattern.split('/').map(function (p) {\n          var name = getParamName(p);\n          if (p === '*') {\n            return route.name;\n          }\n          if (p.startsWith(':')) {\n            var value = allParams[name];\n            if (value === undefined && p.endsWith('?')) {\n              return '';\n            }\n            return encodeURIComponent(value);\n          }\n          return encodeURIComponent(p);\n        }).join('/');\n      } else {\n        path += encodeURIComponent(route.name);\n      }\n      if (!focusedParams) {\n        focusedParams = focusedRoute.params;\n      }\n      if (route.state) {\n        path += '/';\n      } else if (focusedParams) {\n        for (var param in focusedParams) {\n          if (focusedParams[param] === 'undefined') {\n            delete focusedParams[param];\n          }\n        }\n        var query = queryString.stringify(focusedParams, {\n          sort: false\n        });\n        if (query) {\n          path += `?${query}`;\n        }\n      }\n      current = route.state;\n    },\n    _currentOptions$route,\n    _pattern;\n  while (current) {\n    _loop();\n  }\n  path = path.replace(/\\/+/g, '/');\n  path = path.length > 1 ? path.replace(/\\/$/, '') : path;\n  return path;\n}\nvar getParamName = function getParamName(pattern) {\n  return pattern.replace(/^:/, '').replace(/\\?$/, '');\n};\nvar joinPaths = function joinPaths() {\n  var _ref4;\n  for (var _len = arguments.length, paths = new Array(_len), _key = 0; _key < _len; _key++) {\n    paths[_key] = arguments[_key];\n  }\n  return (_ref4 = []).concat.apply(_ref4, _toConsumableArray(paths.map(function (p) {\n    return p.split('/');\n  }))).filter(Boolean).join('/');\n};\nvar createConfigItem = function createConfigItem(config, parentPattern) {\n  var _pattern2;\n  if (typeof config === 'string') {\n    var _pattern3 = parentPattern ? joinPaths(parentPattern, config) : config;\n    return {\n      pattern: _pattern3\n    };\n  }\n  var pattern;\n  if (config.exact && config.path === undefined) {\n    throw new Error(\"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\");\n  }\n  pattern = config.exact !== true ? joinPaths(parentPattern || '', config.path || '') : config.path || '';\n  var screens = config.screens ? createNormalizedConfigs(config.screens, pattern) : undefined;\n  return {\n    pattern: (_pattern2 = pattern) === null || _pattern2 === void 0 ? void 0 : _pattern2.split('/').filter(Boolean).join('/'),\n    stringify: config.stringify,\n    screens: screens\n  };\n};\nvar createNormalizedConfigs = function createNormalizedConfigs(options, pattern) {\n  return fromEntries(Object.entries(options).map(function (_ref2) {\n    var _ref5 = _slicedToArray(_ref2, 2),\n      name = _ref5[0],\n      c = _ref5[1];\n    var result = createConfigItem(c, pattern);\n    return [name, result];\n  }));\n};", "map": {"version": 3, "names": ["queryString", "fromEntries", "validatePathConfig", "getActiveRoute", "state", "route", "index", "routes", "length", "getPathFromState", "options", "Error", "configs", "screens", "createNormalizedConfigs", "path", "current", "allParams", "_loop", "pattern", "focusedParams", "focusedRoute", "currentOptions", "nestedRouteNames", "hasNext", "_loop2", "name", "push", "params", "stringify", "_currentOptions$route", "currentParams", "Object", "entries", "map", "_ref", "_ref3", "_slicedToArray", "key", "value", "String", "assign", "_objectSpread", "_pattern", "split", "filter", "p", "startsWith", "for<PERSON>ach", "getParamName", "undefined", "nextRoute", "nestedConfig", "join", "endsWith", "encodeURIComponent", "param", "query", "sort", "replace", "joinPaths", "_ref4", "_len", "arguments", "paths", "Array", "_key", "concat", "apply", "_toConsumableArray", "Boolean", "createConfigItem", "config", "parentPattern", "_pattern2", "exact", "_ref2", "_ref5", "c", "result"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\getPathFromState.tsx"], "sourcesContent": ["import type {\n  NavigationState,\n  PartialState,\n  Route,\n} from '@react-navigation/routers';\nimport * as queryString from 'query-string';\n\nimport fromEntries from './fromEntries';\nimport type { PathConfig, PathConfigMap } from './types';\nimport validatePathConfig from './validatePathConfig';\n\ntype Options<ParamList extends {}> = {\n  initialRouteName?: string;\n  screens: PathConfigMap<ParamList>;\n};\n\ntype State = NavigationState | Omit<PartialState<NavigationState>, 'stale'>;\n\ntype StringifyConfig = Record<string, (value: any) => string>;\n\ntype ConfigItem = {\n  pattern?: string;\n  stringify?: StringifyConfig;\n  screens?: Record<string, ConfigItem>;\n};\n\nconst getActiveRoute = (state: State): { name: string; params?: object } => {\n  const route =\n    typeof state.index === 'number'\n      ? state.routes[state.index]\n      : state.routes[state.routes.length - 1];\n\n  if (route.state) {\n    return getActiveRoute(route.state);\n  }\n\n  return route;\n};\n\n/**\n * Utility to serialize a navigation state object to a path string.\n *\n * @example\n * ```js\n * getPathFromState(\n *   {\n *     routes: [\n *       {\n *         name: 'Chat',\n *         params: { author: 'Jane', id: 42 },\n *       },\n *     ],\n *   },\n *   {\n *     screens: {\n *       Chat: {\n *         path: 'chat/:author/:id',\n *         stringify: { author: author => author.toLowerCase() }\n *       }\n *     }\n *   }\n * )\n * ```\n *\n * @param state Navigation state to serialize.\n * @param options Extra options to fine-tune how to serialize the path.\n * @returns Path representing the state, e.g. /foo/bar?count=42.\n */\nexport default function getPathFromState<ParamList extends {}>(\n  state: State,\n  options?: Options<ParamList>\n): string {\n  if (state == null) {\n    throw Error(\n      \"Got 'undefined' for the navigation state. You must pass a valid state object.\"\n    );\n  }\n\n  if (options) {\n    validatePathConfig(options);\n  }\n\n  // Create a normalized configs object which will be easier to use\n  const configs: Record<string, ConfigItem> = options?.screens\n    ? createNormalizedConfigs(options?.screens)\n    : {};\n\n  let path = '/';\n  let current: State | undefined = state;\n\n  const allParams: Record<string, any> = {};\n\n  while (current) {\n    let index = typeof current.index === 'number' ? current.index : 0;\n    let route = current.routes[index] as Route<string> & {\n      state?: State;\n    };\n\n    let pattern: string | undefined;\n\n    let focusedParams: Record<string, any> | undefined;\n    let focusedRoute = getActiveRoute(state);\n    let currentOptions = configs;\n\n    // Keep all the route names that appeared during going deeper in config in case the pattern is resolved to undefined\n    let nestedRouteNames = [];\n\n    let hasNext = true;\n\n    while (route.name in currentOptions && hasNext) {\n      pattern = currentOptions[route.name].pattern;\n\n      nestedRouteNames.push(route.name);\n\n      if (route.params) {\n        const stringify = currentOptions[route.name]?.stringify;\n\n        const currentParams = fromEntries(\n          Object.entries(route.params).map(([key, value]) => [\n            key,\n            stringify?.[key] ? stringify[key](value) : String(value),\n          ])\n        );\n\n        if (pattern) {\n          Object.assign(allParams, currentParams);\n        }\n\n        if (focusedRoute === route) {\n          // If this is the focused route, keep the params for later use\n          // We save it here since it's been stringified already\n          focusedParams = { ...currentParams };\n\n          pattern\n            ?.split('/')\n            .filter((p) => p.startsWith(':'))\n            // eslint-disable-next-line no-loop-func\n            .forEach((p) => {\n              const name = getParamName(p);\n\n              // Remove the params present in the pattern since we'll only use the rest for query string\n              if (focusedParams) {\n                // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n                delete focusedParams[name];\n              }\n            });\n        }\n      }\n\n      // If there is no `screens` property or no nested state, we return pattern\n      if (!currentOptions[route.name].screens || route.state === undefined) {\n        hasNext = false;\n      } else {\n        index =\n          typeof route.state.index === 'number'\n            ? route.state.index\n            : route.state.routes.length - 1;\n\n        const nextRoute = route.state.routes[index];\n        const nestedConfig = currentOptions[route.name].screens;\n\n        // if there is config for next route name, we go deeper\n        if (nestedConfig && nextRoute.name in nestedConfig) {\n          route = nextRoute as Route<string> & { state?: State };\n          currentOptions = nestedConfig;\n        } else {\n          // If not, there is no sense in going deeper in config\n          hasNext = false;\n        }\n      }\n    }\n\n    if (pattern === undefined) {\n      pattern = nestedRouteNames.join('/');\n    }\n\n    if (currentOptions[route.name] !== undefined) {\n      path += pattern\n        .split('/')\n        .map((p) => {\n          const name = getParamName(p);\n\n          // We don't know what to show for wildcard patterns\n          // Showing the route name seems ok, though whatever we show here will be incorrect\n          // Since the page doesn't actually exist\n          if (p === '*') {\n            return route.name;\n          }\n\n          // If the path has a pattern for a param, put the param in the path\n          if (p.startsWith(':')) {\n            const value = allParams[name];\n\n            if (value === undefined && p.endsWith('?')) {\n              // Optional params without value assigned in route.params should be ignored\n              return '';\n            }\n\n            return encodeURIComponent(value);\n          }\n\n          return encodeURIComponent(p);\n        })\n        .join('/');\n    } else {\n      path += encodeURIComponent(route.name);\n    }\n\n    if (!focusedParams) {\n      focusedParams = focusedRoute.params;\n    }\n\n    if (route.state) {\n      path += '/';\n    } else if (focusedParams) {\n      for (let param in focusedParams) {\n        if (focusedParams[param] === 'undefined') {\n          // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n          delete focusedParams[param];\n        }\n      }\n\n      const query = queryString.stringify(focusedParams, { sort: false });\n\n      if (query) {\n        path += `?${query}`;\n      }\n    }\n\n    current = route.state;\n  }\n\n  // Remove multiple as well as trailing slashes\n  path = path.replace(/\\/+/g, '/');\n  path = path.length > 1 ? path.replace(/\\/$/, '') : path;\n\n  return path;\n}\n\nconst getParamName = (pattern: string) =>\n  pattern.replace(/^:/, '').replace(/\\?$/, '');\n\nconst joinPaths = (...paths: string[]): string =>\n  ([] as string[])\n    .concat(...paths.map((p) => p.split('/')))\n    .filter(Boolean)\n    .join('/');\n\nconst createConfigItem = (\n  config: PathConfig<object> | string,\n  parentPattern?: string\n): ConfigItem => {\n  if (typeof config === 'string') {\n    // If a string is specified as the value of the key(e.g. Foo: '/path'), use it as the pattern\n    const pattern = parentPattern ? joinPaths(parentPattern, config) : config;\n\n    return { pattern };\n  }\n\n  // If an object is specified as the value (e.g. Foo: { ... }),\n  // It can have `path` property and `screens` prop which has nested configs\n  let pattern: string | undefined;\n\n  if (config.exact && config.path === undefined) {\n    throw new Error(\n      \"A 'path' needs to be specified when specifying 'exact: true'. If you don't want this screen in the URL, specify it as empty string, e.g. `path: ''`.\"\n    );\n  }\n\n  pattern =\n    config.exact !== true\n      ? joinPaths(parentPattern || '', config.path || '')\n      : config.path || '';\n\n  const screens = config.screens\n    ? createNormalizedConfigs(config.screens, pattern)\n    : undefined;\n\n  return {\n    // Normalize pattern to remove any leading, trailing slashes, duplicate slashes etc.\n    pattern: pattern?.split('/').filter(Boolean).join('/'),\n    stringify: config.stringify,\n    screens,\n  };\n};\n\nconst createNormalizedConfigs = (\n  options: PathConfigMap<object>,\n  pattern?: string\n): Record<string, ConfigItem> =>\n  fromEntries(\n    Object.entries(options).map(([name, c]) => {\n      const result = createConfigItem(c, pattern);\n\n      return [name, result];\n    })\n  );\n"], "mappings": ";;;;;AAKA,OAAO,KAAKA,WAAW,MAAM,cAAc;AAE3C,OAAOC,WAAW;AAElB,OAAOC,kBAAkB;AAiBzB,IAAMC,eAAc,GAAI,SAAlBA,cAAcA,CAAIC,KAAY,EAAwC;EAC1E,IAAMC,KAAK,GACT,OAAOD,KAAK,CAACE,KAAK,KAAK,QAAQ,GAC3BF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACE,KAAK,CAAC,GACzBF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;EAE3C,IAAIH,KAAK,CAACD,KAAK,EAAE;IACf,OAAOD,eAAc,CAACE,KAAK,CAACD,KAAK,CAAC;EACpC;EAEA,OAAOC,KAAK;AACd,CAAC;AA+BD,eAAe,SAASI,gBAAgBA,CACtCL,KAAY,EACZM,OAA4B,EACpB;EACR,IAAIN,KAAK,IAAI,IAAI,EAAE;IACjB,MAAMO,KAAK,CACT,+EAA+E,CAChF;EACH;EAEA,IAAID,OAAO,EAAE;IACXR,kBAAkB,CAACQ,OAAO,CAAC;EAC7B;EAGA,IAAME,OAAmC,GAAGF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEG,OAAO,GACxDC,uBAAuB,CAACJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,OAAO,CAAC,GACzC,CAAC,CAAC;EAEN,IAAIE,IAAI,GAAG,GAAG;EACd,IAAIC,OAA0B,GAAGZ,KAAK;EAEtC,IAAMa,SAA8B,GAAG,CAAC,CAAC;EAAA,IAAAC,KAAA,YAAAA,MAAA,EAEzB;MACd,IAAIZ,KAAK,GAAG,OAAOU,OAAO,CAACV,KAAK,KAAK,QAAQ,GAAGU,OAAO,CAACV,KAAK,GAAG,CAAC;MACjE,IAAID,KAAK,GAAGW,OAAO,CAACT,MAAM,CAACD,KAAK,CAE/B;MAED,IAAIa,OAA2B;MAE/B,IAAIC,aAA8C;MAClD,IAAIC,YAAY,GAAGlB,eAAc,CAACC,KAAK,CAAC;MACxC,IAAIkB,cAAc,GAAGV,OAAO;MAG5B,IAAIW,gBAAgB,GAAG,EAAE;MAEzB,IAAIC,OAAO,GAAG,IAAI;MAAA,IAAAC,MAAA,YAAAA,OAAA,EAE8B;QAC9CN,OAAO,GAAGG,cAAc,CAACjB,KAAK,CAACqB,IAAI,CAAC,CAACP,OAAO;QAE5CI,gBAAgB,CAACI,IAAI,CAACtB,KAAK,CAACqB,IAAI,CAAC;QAEjC,IAAIrB,KAAK,CAACuB,MAAM,EAAE;UAChB,IAAMC,SAAS,IAAAC,qBAAA,GAAGR,cAAc,CAACjB,KAAK,CAACqB,IAAI,CAAC,cAAAI,qBAAA,uBAA1BA,qBAAA,CAA4BD,SAAS;UAEvD,IAAME,aAAa,GAAG9B,WAAW,CAC/B+B,MAAM,CAACC,OAAO,CAAC5B,KAAK,CAACuB,MAAM,CAAC,CAACM,GAAG,CAAC,UAAAC,IAAA;YAAA,IAAAC,KAAA,GAAAC,cAAA,CAAaF,IAAA;cAAXG,GAAG,GAAAF,KAAA;cAAEG,KAAK,GAAAH,KAAA;YAAC,OAAK,CACjDE,GAAG,EACHT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGS,GAAG,CAAC,GAAGT,SAAS,CAACS,GAAG,CAAC,CAACC,KAAK,CAAC,GAAGC,MAAM,CAACD,KAAK,CAAC,CACzD;UAAA,EAAC,CACH;UAED,IAAIpB,OAAO,EAAE;YACXa,MAAM,CAACS,MAAM,CAACxB,SAAS,EAAEc,aAAa,CAAC;UACzC;UAEA,IAAIV,YAAY,KAAKhB,KAAK,EAAE;YAG1Be,aAAa,GAAAsB,aAAA,KAAQX,aAAA,CAAe;YAEpC,CAAAY,QAAA,GAAAxB,OAAO,cAAAwB,QAAA,uBAAPA,QAAA,CACIC,KAAK,CAAC,GAAG,CAAC,CACXC,MAAM,CAAE,UAAAC,CAAC;cAAA,OAAKA,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC;YAAA,GAE/BC,OAAO,CAAE,UAAAF,CAAC,EAAK;cACd,IAAMpB,IAAI,GAAGuB,YAAY,CAACH,CAAC,CAAC;cAG5B,IAAI1B,aAAa,EAAE;gBAEjB,OAAOA,aAAa,CAACM,IAAI,CAAC;cAC5B;YACF,CAAC,CAAC;UACN;QACF;QAGA,IAAI,CAACJ,cAAc,CAACjB,KAAK,CAACqB,IAAI,CAAC,CAACb,OAAO,IAAIR,KAAK,CAACD,KAAK,KAAK8C,SAAS,EAAE;UACpE1B,OAAO,GAAG,KAAK;QACjB,CAAC,MAAM;UACLlB,KAAK,GACH,OAAOD,KAAK,CAACD,KAAK,CAACE,KAAK,KAAK,QAAQ,GACjCD,KAAK,CAACD,KAAK,CAACE,KAAK,GACjBD,KAAK,CAACD,KAAK,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC;UAEnC,IAAM2C,SAAS,GAAG9C,KAAK,CAACD,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC;UAC3C,IAAM8C,YAAY,GAAG9B,cAAc,CAACjB,KAAK,CAACqB,IAAI,CAAC,CAACb,OAAO;UAGvD,IAAIuC,YAAY,IAAID,SAAS,CAACzB,IAAI,IAAI0B,YAAY,EAAE;YAClD/C,KAAK,GAAG8C,SAA8C;YACtD7B,cAAc,GAAG8B,YAAY;UAC/B,CAAC,MAAM;YAEL5B,OAAO,GAAG,KAAK;UACjB;QACF;MACF;MA7DA,OAAOnB,KAAK,CAACqB,IAAI,IAAIJ,cAAc,IAAIE,OAAO;QAAAC,MAAA;MAAA;MA+D9C,IAAIN,OAAO,KAAK+B,SAAS,EAAE;QACzB/B,OAAO,GAAGI,gBAAgB,CAAC8B,IAAI,CAAC,GAAG,CAAC;MACtC;MAEA,IAAI/B,cAAc,CAACjB,KAAK,CAACqB,IAAI,CAAC,KAAKwB,SAAS,EAAE;QAC5CnC,IAAI,IAAII,OAAO,CACZyB,KAAK,CAAC,GAAG,CAAC,CACVV,GAAG,CAAE,UAAAY,CAAC,EAAK;UACV,IAAMpB,IAAI,GAAGuB,YAAY,CAACH,CAAC,CAAC;UAK5B,IAAIA,CAAC,KAAK,GAAG,EAAE;YACb,OAAOzC,KAAK,CAACqB,IAAI;UACnB;UAGA,IAAIoB,CAAC,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;YACrB,IAAMR,KAAK,GAAGtB,SAAS,CAACS,IAAI,CAAC;YAE7B,IAAIa,KAAK,KAAKW,SAAS,IAAIJ,CAAC,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;cAE1C,OAAO,EAAE;YACX;YAEA,OAAOC,kBAAkB,CAAChB,KAAK,CAAC;UAClC;UAEA,OAAOgB,kBAAkB,CAACT,CAAC,CAAC;QAC9B,CAAC,CAAC,CACDO,IAAI,CAAC,GAAG,CAAC;MACd,CAAC,MAAM;QACLtC,IAAI,IAAIwC,kBAAkB,CAAClD,KAAK,CAACqB,IAAI,CAAC;MACxC;MAEA,IAAI,CAACN,aAAa,EAAE;QAClBA,aAAa,GAAGC,YAAY,CAACO,MAAM;MACrC;MAEA,IAAIvB,KAAK,CAACD,KAAK,EAAE;QACfW,IAAI,IAAI,GAAG;MACb,CAAC,MAAM,IAAIK,aAAa,EAAE;QACxB,KAAK,IAAIoC,KAAK,IAAIpC,aAAa,EAAE;UAC/B,IAAIA,aAAa,CAACoC,KAAK,CAAC,KAAK,WAAW,EAAE;YAExC,OAAOpC,aAAa,CAACoC,KAAK,CAAC;UAC7B;QACF;QAEA,IAAMC,KAAK,GAAGzD,WAAW,CAAC6B,SAAS,CAACT,aAAa,EAAE;UAAEsC,IAAI,EAAE;QAAM,CAAC,CAAC;QAEnE,IAAID,KAAK,EAAE;UACT1C,IAAI,IAAK,IAAG0C,KAAM,EAAC;QACrB;MACF;MAEAzC,OAAO,GAAGX,KAAK,CAACD,KAAK;IACvB;IAAA0B,qBAAA;IAAAa,QAAA;EA1IA,OAAO3B,OAAO;IAAAE,KAAA;EAAA;EA6IdH,IAAI,GAAGA,IAAI,CAAC4C,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAChC5C,IAAI,GAAGA,IAAI,CAACP,MAAM,GAAG,CAAC,GAAGO,IAAI,CAAC4C,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG5C,IAAI;EAEvD,OAAOA,IAAI;AACb;AAEA,IAAMkC,YAAY,GAAI,SAAhBA,YAAYA,CAAI9B,OAAe;EAAA,OACnCA,OAAO,CAACwC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAAA;AAE9C,IAAMC,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAG;EAAA,IAAAC,KAAA;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAvD,MAAA,EAAIwD,KAAK,OAAAC,KAAA,CAAAH,IAAA,GAAAI,IAAA,MAAAA,IAAA,GAAAJ,IAAA,EAAAI,IAAA;IAALF,KAAK,CAAAE,IAAA,IAAAH,SAAA,CAAAG,IAAA;EAAA;EAAA,OACxB,CAAAL,KAAA,KAAE,EACAM,MAAM,CAAAC,KAAA,CAAAP,KAAA,EAAAQ,kBAAA,CAAIL,KAAK,CAAC9B,GAAG,CAAE,UAAAY,CAAC;IAAA,OAAKA,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC;EAAA,EAAC,EAAC,CACzCC,MAAM,CAACyB,OAAO,CAAC,CACfjB,IAAI,CAAC,GAAG,CAAC;AAAA;AAEd,IAAMkB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CACpBC,MAAmC,EACnCC,aAAsB,EACP;EAAA,IAAAC,SAAA;EACf,IAAI,OAAOF,MAAM,KAAK,QAAQ,EAAE;IAE9B,IAAMrD,SAAO,GAAGsD,aAAa,GAAGb,SAAS,CAACa,aAAa,EAAED,MAAM,CAAC,GAAGA,MAAM;IAEzE,OAAO;MAAErD,OAAA,EAAAA;IAAQ,CAAC;EACpB;EAIA,IAAIA,OAA2B;EAE/B,IAAIqD,MAAM,CAACG,KAAK,IAAIH,MAAM,CAACzD,IAAI,KAAKmC,SAAS,EAAE;IAC7C,MAAM,IAAIvC,KAAK,CACb,sJAAsJ,CACvJ;EACH;EAEAQ,OAAO,GACLqD,MAAM,CAACG,KAAK,KAAK,IAAI,GACjBf,SAAS,CAACa,aAAa,IAAI,EAAE,EAAED,MAAM,CAACzD,IAAI,IAAI,EAAE,CAAC,GACjDyD,MAAM,CAACzD,IAAI,IAAI,EAAE;EAEvB,IAAMF,OAAO,GAAG2D,MAAM,CAAC3D,OAAO,GAC1BC,uBAAuB,CAAC0D,MAAM,CAAC3D,OAAO,EAAEM,OAAO,CAAC,GAChD+B,SAAS;EAEb,OAAO;IAEL/B,OAAO,GAAAuD,SAAA,GAAEvD,OAAO,cAAAuD,SAAA,uBAAPA,SAAA,CAAS9B,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAACyB,OAAO,CAAC,CAACjB,IAAI,CAAC,GAAG,CAAC;IACtDxB,SAAS,EAAE2C,MAAM,CAAC3C,SAAS;IAC3BhB,OAAA,EAAAA;EACF,CAAC;AACH,CAAC;AAED,IAAMC,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAC3BJ,OAA8B,EAC9BS,OAAgB;EAAA,OAEhBlB,WAAW,CACT+B,MAAM,CAACC,OAAO,CAACvB,OAAO,CAAC,CAACwB,GAAG,CAAC,UAAA0C,KAAA,EAAe;IAAA,IAAAC,KAAA,GAAAxC,cAAA,CAALuC,KAAA;MAARlD,IAAI,GAAAmD,KAAA;MAAEC,CAAC,GAAAD,KAAA;IACnC,IAAME,MAAM,GAAGR,gBAAgB,CAACO,CAAC,EAAE3D,OAAO,CAAC;IAE3C,OAAO,CAACO,IAAI,EAAEqD,MAAM,CAAC;EACvB,CAAC,CAAC,CACH;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}