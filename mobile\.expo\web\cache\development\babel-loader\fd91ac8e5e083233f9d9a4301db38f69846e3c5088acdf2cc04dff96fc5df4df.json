{"ast": null, "code": "import canUseDOM from \"../../../modules/canUseDom\";\nimport createCSSStyleSheet from \"./createCSSStyleSheet\";\nimport createOrderedCSSStyleSheet from \"./createOrderedCSSStyleSheet\";\nvar defaultId = 'react-native-stylesheet';\nvar roots = new WeakMap();\nvar sheets = [];\nvar initialRules = ['html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:rgba(0,0,0,0);}', 'body{margin:0;}', 'button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0;}', 'input::-webkit-search-cancel-button,input::-webkit-search-decoration,input::-webkit-search-results-button,input::-webkit-search-results-decoration{display:none;}'];\nexport function createSheet(root, id) {\n  if (id === void 0) {\n    id = defaultId;\n  }\n  var sheet;\n  if (canUseDOM) {\n    var rootNode = root != null ? root.getRootNode() : document;\n    if (sheets.length === 0) {\n      sheet = createOrderedCSSStyleSheet(createCSSStyleSheet(id));\n      initialRules.forEach(function (rule) {\n        sheet.insert(rule, 0);\n      });\n      roots.set(rootNode, sheets.length);\n      sheets.push(sheet);\n    } else {\n      var index = roots.get(rootNode);\n      if (index == null) {\n        var initialSheet = sheets[0];\n        var textContent = initialSheet != null ? initialSheet.getTextContent() : '';\n        sheet = createOrderedCSSStyleSheet(createCSSStyleSheet(id, rootNode, textContent));\n        roots.set(rootNode, sheets.length);\n        sheets.push(sheet);\n      } else {\n        sheet = sheets[index];\n      }\n    }\n  } else {\n    if (sheets.length === 0) {\n      sheet = createOrderedCSSStyleSheet(createCSSStyleSheet(id));\n      initialRules.forEach(function (rule) {\n        sheet.insert(rule, 0);\n      });\n      sheets.push(sheet);\n    } else {\n      sheet = sheets[0];\n    }\n  }\n  return {\n    getTextContent: function getTextContent() {\n      return sheet.getTextContent();\n    },\n    id: id,\n    insert: function insert(cssText, groupValue) {\n      sheets.forEach(function (s) {\n        s.insert(cssText, groupValue);\n      });\n    }\n  };\n}", "map": {"version": 3, "names": ["canUseDOM", "createCSSStyleSheet", "createOrderedCSSStyleSheet", "defaultId", "roots", "WeakMap", "sheets", "initialRules", "createSheet", "root", "id", "sheet", "rootNode", "getRootNode", "document", "length", "for<PERSON>ach", "rule", "insert", "set", "push", "index", "get", "initialSheet", "textContent", "getTextContent", "cssText", "groupValue", "s"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/exports/StyleSheet/dom/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport canUseDOM from '../../../modules/canUseDom';\nimport createCSSStyleSheet from './createCSSStyleSheet';\nimport createOrderedCSSStyleSheet from './createOrderedCSSStyleSheet';\nvar defaultId = 'react-native-stylesheet';\nvar roots = new WeakMap();\nvar sheets = [];\nvar initialRules = [\n// minimal top-level reset\n'html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:rgba(0,0,0,0);}', 'body{margin:0;}',\n// minimal form pseudo-element reset\n'button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0;}', 'input::-webkit-search-cancel-button,input::-webkit-search-decoration,input::-webkit-search-results-button,input::-webkit-search-results-decoration{display:none;}'];\nexport function createSheet(root, id) {\n  if (id === void 0) {\n    id = defaultId;\n  }\n  var sheet;\n  if (canUseDOM) {\n    var rootNode = root != null ? root.getRootNode() : document;\n    // Create the initial style sheet\n    if (sheets.length === 0) {\n      sheet = createOrderedCSSStyleSheet(createCSSStyleSheet(id));\n      initialRules.forEach(rule => {\n        sheet.insert(rule, 0);\n      });\n      roots.set(rootNode, sheets.length);\n      sheets.push(sheet);\n    } else {\n      var index = roots.get(rootNode);\n      if (index == null) {\n        var initialSheet = sheets[0];\n        // If we're creating a new sheet, populate it with existing styles\n        var textContent = initialSheet != null ? initialSheet.getTextContent() : '';\n        // Cast rootNode to 'any' because Flow types for getRootNode are wrong\n        sheet = createOrderedCSSStyleSheet(createCSSStyleSheet(id, rootNode, textContent));\n        roots.set(rootNode, sheets.length);\n        sheets.push(sheet);\n      } else {\n        sheet = sheets[index];\n      }\n    }\n  } else {\n    // Create the initial style sheet\n    if (sheets.length === 0) {\n      sheet = createOrderedCSSStyleSheet(createCSSStyleSheet(id));\n      initialRules.forEach(rule => {\n        sheet.insert(rule, 0);\n      });\n      sheets.push(sheet);\n    } else {\n      sheet = sheets[0];\n    }\n  }\n  return {\n    getTextContent() {\n      return sheet.getTextContent();\n    },\n    id,\n    insert(cssText, groupValue) {\n      sheets.forEach(s => {\n        s.insert(cssText, groupValue);\n      });\n    }\n  };\n}"], "mappings": "AASA,OAAOA,SAAS;AAChB,OAAOC,mBAAmB;AAC1B,OAAOC,0BAA0B;AACjC,IAAIC,SAAS,GAAG,yBAAyB;AACzC,IAAIC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzB,IAAIC,MAAM,GAAG,EAAE;AACf,IAAIC,YAAY,GAAG,CAEnB,0GAA0G,EAAE,iBAAiB,EAE7H,uEAAuE,EAAE,mKAAmK,CAAC;AAC7O,OAAO,SAASC,WAAWA,CAACC,IAAI,EAAEC,EAAE,EAAE;EACpC,IAAIA,EAAE,KAAK,KAAK,CAAC,EAAE;IACjBA,EAAE,GAAGP,SAAS;EAChB;EACA,IAAIQ,KAAK;EACT,IAAIX,SAAS,EAAE;IACb,IAAIY,QAAQ,GAAGH,IAAI,IAAI,IAAI,GAAGA,IAAI,CAACI,WAAW,CAAC,CAAC,GAAGC,QAAQ;IAE3D,IAAIR,MAAM,CAACS,MAAM,KAAK,CAAC,EAAE;MACvBJ,KAAK,GAAGT,0BAA0B,CAACD,mBAAmB,CAACS,EAAE,CAAC,CAAC;MAC3DH,YAAY,CAACS,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BN,KAAK,CAACO,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC;MACFb,KAAK,CAACe,GAAG,CAACP,QAAQ,EAAEN,MAAM,CAACS,MAAM,CAAC;MAClCT,MAAM,CAACc,IAAI,CAACT,KAAK,CAAC;IACpB,CAAC,MAAM;MACL,IAAIU,KAAK,GAAGjB,KAAK,CAACkB,GAAG,CAACV,QAAQ,CAAC;MAC/B,IAAIS,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIE,YAAY,GAAGjB,MAAM,CAAC,CAAC,CAAC;QAE5B,IAAIkB,WAAW,GAAGD,YAAY,IAAI,IAAI,GAAGA,YAAY,CAACE,cAAc,CAAC,CAAC,GAAG,EAAE;QAE3Ed,KAAK,GAAGT,0BAA0B,CAACD,mBAAmB,CAACS,EAAE,EAAEE,QAAQ,EAAEY,WAAW,CAAC,CAAC;QAClFpB,KAAK,CAACe,GAAG,CAACP,QAAQ,EAAEN,MAAM,CAACS,MAAM,CAAC;QAClCT,MAAM,CAACc,IAAI,CAACT,KAAK,CAAC;MACpB,CAAC,MAAM;QACLA,KAAK,GAAGL,MAAM,CAACe,KAAK,CAAC;MACvB;IACF;EACF,CAAC,MAAM;IAEL,IAAIf,MAAM,CAACS,MAAM,KAAK,CAAC,EAAE;MACvBJ,KAAK,GAAGT,0BAA0B,CAACD,mBAAmB,CAACS,EAAE,CAAC,CAAC;MAC3DH,YAAY,CAACS,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BN,KAAK,CAACO,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC;MACvB,CAAC,CAAC;MACFX,MAAM,CAACc,IAAI,CAACT,KAAK,CAAC;IACpB,CAAC,MAAM;MACLA,KAAK,GAAGL,MAAM,CAAC,CAAC,CAAC;IACnB;EACF;EACA,OAAO;IACLmB,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,OAAOd,KAAK,CAACc,cAAc,CAAC,CAAC;IAC/B,CAAC;IACDf,EAAE,EAAFA,EAAE;IACFQ,MAAM,WAANA,MAAMA,CAACQ,OAAO,EAAEC,UAAU,EAAE;MAC1BrB,MAAM,CAACU,OAAO,CAAC,UAAAY,CAAC,EAAI;QAClBA,CAAC,CAACV,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}