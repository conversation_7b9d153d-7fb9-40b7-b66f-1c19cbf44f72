{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport { findFocusedRoute, getActionFromState as getActionFromStateDefault, getPathFromState as getPathFromStateDefault, getStateFromPath as getStateFromPathDefault } from '@react-navigation/core';\nimport isEqual from 'fast-deep-equal';\nimport * as React from 'react';\nimport createMemoryHistory from \"./createMemoryHistory\";\nimport ServerContext from \"./ServerContext\";\nvar _findMatchingState = function findMatchingState(a, b) {\n  if (a === undefined || b === undefined || a.key !== b.key) {\n    return [undefined, undefined];\n  }\n  var aHistoryLength = a.history ? a.history.length : a.routes.length;\n  var bHistoryLength = b.history ? b.history.length : b.routes.length;\n  var aRoute = a.routes[a.index];\n  var bRoute = b.routes[b.index];\n  var aChildState = aRoute.state;\n  var bChildState = bRoute.state;\n  if (aHistoryLength !== bHistoryLength || aRoute.key !== bRoute.key || aChildState === undefined || bChildState === undefined || aChildState.key !== bChildState.key) {\n    return [a, b];\n  }\n  return _findMatchingState(aChildState, bChildState);\n};\nexport var series = function series(cb) {\n  var queue = Promise.resolve();\n  var callback = function callback() {\n    queue = queue.then(cb);\n  };\n  return callback;\n};\nvar linkingHandlers = [];\nexport default function useLinking(ref, _ref) {\n  var independent = _ref.independent,\n    _ref$enabled = _ref.enabled,\n    enabled = _ref$enabled === void 0 ? true : _ref$enabled,\n    config = _ref.config,\n    _ref$getStateFromPath = _ref.getStateFromPath,\n    getStateFromPath = _ref$getStateFromPath === void 0 ? getStateFromPathDefault : _ref$getStateFromPath,\n    _ref$getPathFromState = _ref.getPathFromState,\n    getPathFromState = _ref$getPathFromState === void 0 ? getPathFromStateDefault : _ref$getPathFromState,\n    _ref$getActionFromSta = _ref.getActionFromState,\n    getActionFromState = _ref$getActionFromSta === void 0 ? getActionFromStateDefault : _ref$getActionFromSta;\n  React.useEffect(function () {\n    if (process.env.NODE_ENV === 'production') {\n      return undefined;\n    }\n    if (independent) {\n      return undefined;\n    }\n    if (enabled !== false && linkingHandlers.length) {\n      console.error(['Looks like you have configured linking in multiple places. This is likely an error since deep links should only be handled in one place to avoid conflicts. Make sure that:', \"- You don't have multiple NavigationContainers in the app each with 'linking' enabled\", '- Only a single instance of the root component is rendered'].join('\\n').trim());\n    }\n    var handler = Symbol();\n    if (enabled !== false) {\n      linkingHandlers.push(handler);\n    }\n    return function () {\n      var index = linkingHandlers.indexOf(handler);\n      if (index > -1) {\n        linkingHandlers.splice(index, 1);\n      }\n    };\n  }, [enabled, independent]);\n  var _React$useState = React.useState(createMemoryHistory),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    history = _React$useState2[0];\n  var enabledRef = React.useRef(enabled);\n  var configRef = React.useRef(config);\n  var getStateFromPathRef = React.useRef(getStateFromPath);\n  var getPathFromStateRef = React.useRef(getPathFromState);\n  var getActionFromStateRef = React.useRef(getActionFromState);\n  React.useEffect(function () {\n    enabledRef.current = enabled;\n    configRef.current = config;\n    getStateFromPathRef.current = getStateFromPath;\n    getPathFromStateRef.current = getPathFromState;\n    getActionFromStateRef.current = getActionFromState;\n  });\n  var server = React.useContext(ServerContext);\n  var getInitialState = React.useCallback(function () {\n    var value;\n    if (enabledRef.current) {\n      var _ref2;\n      var location = (_ref2 = server === null || server === void 0 ? void 0 : server.location) != null ? _ref2 : typeof window !== 'undefined' ? window.location : undefined;\n      var path = location ? location.pathname + location.search : undefined;\n      if (path) {\n        value = getStateFromPathRef.current(path, configRef.current);\n      }\n    }\n    var thenable = {\n      then: function then(onfulfilled) {\n        return Promise.resolve(onfulfilled ? onfulfilled(value) : value);\n      },\n      catch: function _catch() {\n        return thenable;\n      }\n    };\n    return thenable;\n  }, []);\n  var previousIndexRef = React.useRef(undefined);\n  var previousStateRef = React.useRef(undefined);\n  var pendingPopStatePathRef = React.useRef(undefined);\n  React.useEffect(function () {\n    previousIndexRef.current = history.index;\n    return history.listen(function () {\n      var _previousIndexRef$cur;\n      var navigation = ref.current;\n      if (!navigation || !enabled) {\n        return;\n      }\n      var _window = window,\n        location = _window.location;\n      var path = location.pathname + location.search;\n      var index = history.index;\n      var previousIndex = (_previousIndexRef$cur = previousIndexRef.current) != null ? _previousIndexRef$cur : 0;\n      previousIndexRef.current = index;\n      pendingPopStatePathRef.current = path;\n      var record = history.get(index);\n      if ((record === null || record === void 0 ? void 0 : record.path) === path && record !== null && record !== void 0 && record.state) {\n        navigation.resetRoot(record.state);\n        return;\n      }\n      var state = getStateFromPathRef.current(path, configRef.current);\n      if (state) {\n        var rootState = navigation.getRootState();\n        if (state.routes.some(function (r) {\n          return !(rootState !== null && rootState !== void 0 && rootState.routeNames.includes(r.name));\n        })) {\n          console.warn(\"The navigation state parsed from the URL contains routes not present in the root navigator. This usually means that the linking configuration doesn't match the navigation structure. See https://reactnavigation.org/docs/configuring-links for more details on how to specify a linking configuration.\");\n          return;\n        }\n        if (index > previousIndex) {\n          var action = getActionFromStateRef.current(state, configRef.current);\n          if (action !== undefined) {\n            try {\n              navigation.dispatch(action);\n            } catch (e) {\n              console.warn(`An error occurred when trying to handle the link '${path}': ${typeof e === 'object' && e != null && 'message' in e ? e.message : e}`);\n            }\n          } else {\n            navigation.resetRoot(state);\n          }\n        } else {\n          navigation.resetRoot(state);\n        }\n      } else {\n        navigation.resetRoot(state);\n      }\n    });\n  }, [enabled, history, ref]);\n  React.useEffect(function () {\n    var _ref$current;\n    if (!enabled) {\n      return;\n    }\n    var getPathForRoute = function getPathForRoute(route, state) {\n      if (route !== null && route !== void 0 && route.path) {\n        var stateForPath = getStateFromPathRef.current(route.path, configRef.current);\n        if (stateForPath) {\n          var focusedRoute = findFocusedRoute(stateForPath);\n          if (focusedRoute && focusedRoute.name === route.name && isEqual(focusedRoute.params, route.params)) {\n            return route.path;\n          }\n        }\n      }\n      return getPathFromStateRef.current(state, configRef.current);\n    };\n    if (ref.current) {\n      var state = ref.current.getRootState();\n      if (state) {\n        var route = findFocusedRoute(state);\n        var path = getPathForRoute(route, state);\n        if (previousStateRef.current === undefined) {\n          previousStateRef.current = state;\n        }\n        history.replace({\n          path: path,\n          state: state\n        });\n      }\n    }\n    var onStateChange = function () {\n      var _ref3 = _asyncToGenerator(function* () {\n        var navigation = ref.current;\n        if (!navigation || !enabled) {\n          return;\n        }\n        var previousState = previousStateRef.current;\n        var state = navigation.getRootState();\n        if (!state) {\n          return;\n        }\n        var pendingPath = pendingPopStatePathRef.current;\n        var route = findFocusedRoute(state);\n        var path = getPathForRoute(route, state);\n        previousStateRef.current = state;\n        pendingPopStatePathRef.current = undefined;\n        var _findMatchingState2 = _findMatchingState(previousState, state),\n          _findMatchingState3 = _slicedToArray(_findMatchingState2, 2),\n          previousFocusedState = _findMatchingState3[0],\n          focusedState = _findMatchingState3[1];\n        if (previousFocusedState && focusedState && path !== pendingPath) {\n          var historyDelta = (focusedState.history ? focusedState.history.length : focusedState.routes.length) - (previousFocusedState.history ? previousFocusedState.history.length : previousFocusedState.routes.length);\n          if (historyDelta > 0) {\n            history.push({\n              path: path,\n              state: state\n            });\n          } else if (historyDelta < 0) {\n            var nextIndex = history.backIndex({\n              path: path\n            });\n            var currentIndex = history.index;\n            try {\n              if (nextIndex !== -1 && nextIndex < currentIndex && history.get(nextIndex - currentIndex)) {\n                yield history.go(nextIndex - currentIndex);\n              } else {\n                yield history.go(historyDelta);\n              }\n              history.replace({\n                path: path,\n                state: state\n              });\n            } catch (e) {}\n          } else {\n            history.replace({\n              path: path,\n              state: state\n            });\n          }\n        } else {\n          history.replace({\n            path: path,\n            state: state\n          });\n        }\n      });\n      return function onStateChange() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    return (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.addListener('state', series(onStateChange));\n  }, [enabled, history, ref]);\n  return {\n    getInitialState: getInitialState\n  };\n}", "map": {"version": 3, "names": ["findFocusedRoute", "getActionFromState", "getActionFromStateDefault", "getPathFromState", "getPathFromStateDefault", "getStateFromPath", "getStateFromPathDefault", "isEqual", "React", "createMemoryHistory", "ServerContext", "findMatchingState", "a", "b", "undefined", "key", "a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "history", "length", "routes", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aRoute", "index", "bRoute", "aChildState", "state", "bChildState", "series", "cb", "queue", "Promise", "resolve", "callback", "then", "linkingHandlers", "useLinking", "ref", "_ref", "independent", "_ref$enabled", "enabled", "config", "_ref$getStateFromPath", "_ref$getPathFromState", "_ref$getActionFromSta", "useEffect", "process", "env", "NODE_ENV", "console", "error", "join", "trim", "handler", "Symbol", "push", "indexOf", "splice", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "enabledRef", "useRef", "configRef", "getStateFromPathRef", "getPathFromStateRef", "getActionFromStateRef", "current", "server", "useContext", "getInitialState", "useCallback", "value", "_ref2", "location", "window", "path", "pathname", "search", "thenable", "onfulfilled", "catch", "previousIndexRef", "previousStateRef", "pendingPopStatePathRef", "listen", "_previousIndexRef$cur", "navigation", "_window", "previousIndex", "record", "get", "resetRoot", "rootState", "getRootState", "some", "r", "routeNames", "includes", "name", "warn", "action", "dispatch", "e", "message", "_ref$current", "getPathForRoute", "route", "stateForPath", "focusedRoute", "params", "replace", "onStateChange", "_ref3", "_asyncToGenerator", "previousState", "pending<PERSON><PERSON>", "_findMatchingState2", "_findMatchingState3", "previousFocusedState", "focusedState", "history<PERSON><PERSON><PERSON>", "nextIndex", "backIndex", "currentIndex", "go", "apply", "arguments", "addListener"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native\\src\\useLinking.tsx"], "sourcesContent": ["import {\n  findFocused<PERSON>oute,\n  getActionFromState as getActionFromStateDefault,\n  getPathFromState as getPathFromStateDefault,\n  getStateFromPath as getStateFromPathDefault,\n  NavigationContainerRef,\n  NavigationState,\n  ParamListBase,\n} from '@react-navigation/core';\nimport isEqual from 'fast-deep-equal';\nimport * as React from 'react';\n\nimport createMemoryHistory from './createMemoryHistory';\nimport ServerContext from './ServerContext';\nimport type { LinkingOptions } from './types';\n\ntype ResultState = ReturnType<typeof getStateFromPathDefault>;\n\n/**\n * Find the matching navigation state that changed between 2 navigation states\n * e.g.: a -> b -> c -> d and a -> b -> c -> e -> f, if history in b changed, b is the matching state\n */\nconst findMatchingState = <T extends NavigationState>(\n  a: T | undefined,\n  b: T | undefined\n): [T | undefined, T | undefined] => {\n  if (a === undefined || b === undefined || a.key !== b.key) {\n    return [undefined, undefined];\n  }\n\n  // Tab and drawer will have `history` property, but stack will have history in `routes`\n  const aHistoryLength = a.history ? a.history.length : a.routes.length;\n  const bHistoryLength = b.history ? b.history.length : b.routes.length;\n\n  const aRoute = a.routes[a.index];\n  const bRoute = b.routes[b.index];\n\n  const aChildState = aRoute.state as T | undefined;\n  const bChildState = bRoute.state as T | undefined;\n\n  // Stop here if this is the state object that changed:\n  // - history length is different\n  // - focused routes are different\n  // - one of them doesn't have child state\n  // - child state keys are different\n  if (\n    aHistoryLength !== bHistoryLength ||\n    aRoute.key !== bRoute.key ||\n    aChildState === undefined ||\n    bChildState === undefined ||\n    aChildState.key !== bChildState.key\n  ) {\n    return [a, b];\n  }\n\n  return findMatchingState(aChildState, bChildState);\n};\n\n/**\n * Run async function in series as it's called.\n */\nexport const series = (cb: () => Promise<void>) => {\n  let queue = Promise.resolve();\n  const callback = () => {\n    queue = queue.then(cb);\n  };\n  return callback;\n};\n\nlet linkingHandlers: Symbol[] = [];\n\ntype Options = LinkingOptions<ParamListBase> & {\n  independent?: boolean;\n};\n\nexport default function useLinking(\n  ref: React.RefObject<NavigationContainerRef<ParamListBase>>,\n  {\n    independent,\n    enabled = true,\n    config,\n    getStateFromPath = getStateFromPathDefault,\n    getPathFromState = getPathFromStateDefault,\n    getActionFromState = getActionFromStateDefault,\n  }: Options\n) {\n  React.useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return undefined;\n    }\n\n    if (independent) {\n      return undefined;\n    }\n\n    if (enabled !== false && linkingHandlers.length) {\n      console.error(\n        [\n          'Looks like you have configured linking in multiple places. This is likely an error since deep links should only be handled in one place to avoid conflicts. Make sure that:',\n          \"- You don't have multiple NavigationContainers in the app each with 'linking' enabled\",\n          '- Only a single instance of the root component is rendered',\n        ]\n          .join('\\n')\n          .trim()\n      );\n    }\n\n    const handler = Symbol();\n\n    if (enabled !== false) {\n      linkingHandlers.push(handler);\n    }\n\n    return () => {\n      const index = linkingHandlers.indexOf(handler);\n\n      if (index > -1) {\n        linkingHandlers.splice(index, 1);\n      }\n    };\n  }, [enabled, independent]);\n\n  const [history] = React.useState(createMemoryHistory);\n\n  // We store these options in ref to avoid re-creating getInitialState and re-subscribing listeners\n  // This lets user avoid wrapping the items in `React.useCallback` or `React.useMemo`\n  // Not re-creating `getInitialState` is important coz it makes it easier for the user to use in an effect\n  const enabledRef = React.useRef(enabled);\n  const configRef = React.useRef(config);\n  const getStateFromPathRef = React.useRef(getStateFromPath);\n  const getPathFromStateRef = React.useRef(getPathFromState);\n  const getActionFromStateRef = React.useRef(getActionFromState);\n\n  React.useEffect(() => {\n    enabledRef.current = enabled;\n    configRef.current = config;\n    getStateFromPathRef.current = getStateFromPath;\n    getPathFromStateRef.current = getPathFromState;\n    getActionFromStateRef.current = getActionFromState;\n  });\n\n  const server = React.useContext(ServerContext);\n\n  const getInitialState = React.useCallback(() => {\n    let value: ResultState | undefined;\n\n    if (enabledRef.current) {\n      const location =\n        server?.location ??\n        (typeof window !== 'undefined' ? window.location : undefined);\n\n      const path = location ? location.pathname + location.search : undefined;\n\n      if (path) {\n        value = getStateFromPathRef.current(path, configRef.current);\n      }\n    }\n\n    const thenable = {\n      then(onfulfilled?: (state: ResultState | undefined) => void) {\n        return Promise.resolve(onfulfilled ? onfulfilled(value) : value);\n      },\n      catch() {\n        return thenable;\n      },\n    };\n\n    return thenable as PromiseLike<ResultState | undefined>;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const previousIndexRef = React.useRef<number | undefined>(undefined);\n  const previousStateRef = React.useRef<NavigationState | undefined>(undefined);\n  const pendingPopStatePathRef = React.useRef<string | undefined>(undefined);\n\n  React.useEffect(() => {\n    previousIndexRef.current = history.index;\n\n    return history.listen(() => {\n      const navigation = ref.current;\n\n      if (!navigation || !enabled) {\n        return;\n      }\n\n      const { location } = window;\n\n      const path = location.pathname + location.search;\n      const index = history.index;\n\n      const previousIndex = previousIndexRef.current ?? 0;\n\n      previousIndexRef.current = index;\n      pendingPopStatePathRef.current = path;\n\n      // When browser back/forward is clicked, we first need to check if state object for this index exists\n      // If it does we'll reset to that state object\n      // Otherwise, we'll handle it like a regular deep link\n      const record = history.get(index);\n\n      if (record?.path === path && record?.state) {\n        navigation.resetRoot(record.state);\n        return;\n      }\n\n      const state = getStateFromPathRef.current(path, configRef.current);\n\n      // We should only dispatch an action when going forward\n      // Otherwise the action will likely add items to history, which would mess things up\n      if (state) {\n        // Make sure that the routes in the state exist in the root navigator\n        // Otherwise there's an error in the linking configuration\n        const rootState = navigation.getRootState();\n\n        if (state.routes.some((r) => !rootState?.routeNames.includes(r.name))) {\n          console.warn(\n            \"The navigation state parsed from the URL contains routes not present in the root navigator. This usually means that the linking configuration doesn't match the navigation structure. See https://reactnavigation.org/docs/configuring-links for more details on how to specify a linking configuration.\"\n          );\n          return;\n        }\n\n        if (index > previousIndex) {\n          const action = getActionFromStateRef.current(\n            state,\n            configRef.current\n          );\n\n          if (action !== undefined) {\n            try {\n              navigation.dispatch(action);\n            } catch (e) {\n              // Ignore any errors from deep linking.\n              // This could happen in case of malformed links, navigation object not being initialized etc.\n              console.warn(\n                `An error occurred when trying to handle the link '${path}': ${\n                  typeof e === 'object' && e != null && 'message' in e\n                    ? e.message\n                    : e\n                }`\n              );\n            }\n          } else {\n            navigation.resetRoot(state);\n          }\n        } else {\n          navigation.resetRoot(state);\n        }\n      } else {\n        // if current path didn't return any state, we should revert to initial state\n        navigation.resetRoot(state);\n      }\n    });\n  }, [enabled, history, ref]);\n\n  React.useEffect(() => {\n    if (!enabled) {\n      return;\n    }\n\n    const getPathForRoute = (\n      route: ReturnType<typeof findFocusedRoute>,\n      state: NavigationState\n    ): string => {\n      // If the `route` object contains a `path`, use that path as long as `route.name` and `params` still match\n      // This makes sure that we preserve the original URL for wildcard routes\n      if (route?.path) {\n        const stateForPath = getStateFromPathRef.current(\n          route.path,\n          configRef.current\n        );\n\n        if (stateForPath) {\n          const focusedRoute = findFocusedRoute(stateForPath);\n\n          if (\n            focusedRoute &&\n            focusedRoute.name === route.name &&\n            isEqual(focusedRoute.params, route.params)\n          ) {\n            return route.path;\n          }\n        }\n      }\n\n      return getPathFromStateRef.current(state, configRef.current);\n    };\n\n    if (ref.current) {\n      // We need to record the current metadata on the first render if they aren't set\n      // This will allow the initial state to be in the history entry\n      const state = ref.current.getRootState();\n\n      if (state) {\n        const route = findFocusedRoute(state);\n        const path = getPathForRoute(route, state);\n\n        if (previousStateRef.current === undefined) {\n          previousStateRef.current = state;\n        }\n\n        history.replace({ path, state });\n      }\n    }\n\n    const onStateChange = async () => {\n      const navigation = ref.current;\n\n      if (!navigation || !enabled) {\n        return;\n      }\n\n      const previousState = previousStateRef.current;\n      const state = navigation.getRootState();\n\n      // root state may not available, for example when root navigators switch inside the container\n      if (!state) {\n        return;\n      }\n\n      const pendingPath = pendingPopStatePathRef.current;\n      const route = findFocusedRoute(state);\n      const path = getPathForRoute(route, state);\n\n      previousStateRef.current = state;\n      pendingPopStatePathRef.current = undefined;\n\n      // To detect the kind of state change, we need to:\n      // - Find the common focused navigation state in previous and current state\n      // - If only the route keys changed, compare history/routes.length to check if we go back/forward/replace\n      // - If no common focused navigation state found, it's a replace\n      const [previousFocusedState, focusedState] = findMatchingState(\n        previousState,\n        state\n      );\n\n      if (\n        previousFocusedState &&\n        focusedState &&\n        // We should only handle push/pop if path changed from what was in last `popstate`\n        // Otherwise it's likely a change triggered by `popstate`\n        path !== pendingPath\n      ) {\n        const historyDelta =\n          (focusedState.history\n            ? focusedState.history.length\n            : focusedState.routes.length) -\n          (previousFocusedState.history\n            ? previousFocusedState.history.length\n            : previousFocusedState.routes.length);\n\n        if (historyDelta > 0) {\n          // If history length is increased, we should pushState\n          // Note that path might not actually change here, for example, drawer open should pushState\n          history.push({ path, state });\n        } else if (historyDelta < 0) {\n          // If history length is decreased, i.e. entries were removed, we want to go back\n\n          const nextIndex = history.backIndex({ path });\n          const currentIndex = history.index;\n\n          try {\n            if (\n              nextIndex !== -1 &&\n              nextIndex < currentIndex &&\n              // We should only go back if the entry exists and it's less than current index\n              history.get(nextIndex - currentIndex)\n            ) {\n              // An existing entry for this path exists and it's less than current index, go back to that\n              await history.go(nextIndex - currentIndex);\n            } else {\n              // We couldn't find an existing entry to go back to, so we'll go back by the delta\n              // This won't be correct if multiple routes were pushed in one go before\n              // Usually this shouldn't happen and this is a fallback for that\n              await history.go(historyDelta);\n            }\n\n            // Store the updated state as well as fix the path if incorrect\n            history.replace({ path, state });\n          } catch (e) {\n            // The navigation was interrupted\n          }\n        } else {\n          // If history length is unchanged, we want to replaceState\n          history.replace({ path, state });\n        }\n      } else {\n        // If no common navigation state was found, assume it's a replace\n        // This would happen if the user did a reset/conditionally changed navigators\n        history.replace({ path, state });\n      }\n    };\n\n    // We debounce onStateChange coz we don't want multiple state changes to be handled at one time\n    // This could happen since `history.go(n)` is asynchronous\n    // If `pushState` or `replaceState` were called before `history.go(n)` completes, it'll mess stuff up\n    return ref.current?.addListener('state', series(onStateChange));\n  }, [enabled, history, ref]);\n\n  return {\n    getInitialState,\n  };\n}\n"], "mappings": ";;AAAA,SACEA,gBAAgB,EAChBC,kBAAkB,IAAIC,yBAAyB,EAC/CC,gBAAgB,IAAIC,uBAAuB,EAC3CC,gBAAgB,IAAIC,uBAAuB,QAItC,wBAAwB;AAC/B,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,mBAAmB;AAC1B,OAAOC,aAAa;AASpB,IAAMC,kBAAiB,GAAG,SAApBA,iBAAiBA,CACrBC,CAAgB,EAChBC,CAAgB,EACmB;EACnC,IAAID,CAAC,KAAKE,SAAS,IAAID,CAAC,KAAKC,SAAS,IAAIF,CAAC,CAACG,GAAG,KAAKF,CAAC,CAACE,GAAG,EAAE;IACzD,OAAO,CAACD,SAAS,EAAEA,SAAS,CAAC;EAC/B;EAGA,IAAME,cAAc,GAAGJ,CAAC,CAACK,OAAO,GAAGL,CAAC,CAACK,OAAO,CAACC,MAAM,GAAGN,CAAC,CAACO,MAAM,CAACD,MAAM;EACrE,IAAME,cAAc,GAAGP,CAAC,CAACI,OAAO,GAAGJ,CAAC,CAACI,OAAO,CAACC,MAAM,GAAGL,CAAC,CAACM,MAAM,CAACD,MAAM;EAErE,IAAMG,MAAM,GAAGT,CAAC,CAACO,MAAM,CAACP,CAAC,CAACU,KAAK,CAAC;EAChC,IAAMC,MAAM,GAAGV,CAAC,CAACM,MAAM,CAACN,CAAC,CAACS,KAAK,CAAC;EAEhC,IAAME,WAAW,GAAGH,MAAM,CAACI,KAAsB;EACjD,IAAMC,WAAW,GAAGH,MAAM,CAACE,KAAsB;EAOjD,IACET,cAAc,KAAKI,cAAc,IACjCC,MAAM,CAACN,GAAG,KAAKQ,MAAM,CAACR,GAAG,IACzBS,WAAW,KAAKV,SAAS,IACzBY,WAAW,KAAKZ,SAAS,IACzBU,WAAW,CAACT,GAAG,KAAKW,WAAW,CAACX,GAAG,EACnC;IACA,OAAO,CAACH,CAAC,EAAEC,CAAC,CAAC;EACf;EAEA,OAAOF,kBAAiB,CAACa,WAAW,EAAEE,WAAW,CAAC;AACpD,CAAC;AAKD,OAAO,IAAMC,MAAM,GAAI,SAAVA,MAAMA,CAAIC,EAAuB,EAAK;EACjD,IAAIC,KAAK,GAAGC,OAAO,CAACC,OAAO,EAAE;EAC7B,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;IACrBH,KAAK,GAAGA,KAAK,CAACI,IAAI,CAACL,EAAE,CAAC;EACxB,CAAC;EACD,OAAOI,QAAQ;AACjB,CAAC;AAED,IAAIE,eAAyB,GAAG,EAAE;AAMlC,eAAe,SAASC,UAAUA,CAChCC,GAA2D,EAAAC,IAAA,EAS3D;EAAA,IAPEC,WAAW,GAMHD,IAAA,CANRC,WAAW;IAAAC,YAAA,GAMHF,IAAA,CALRG,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IACdE,MAAM,GAIEJ,IAAA,CAJRI,MAAM;IAAAC,qBAAA,GAIEL,IAAA,CAHRhC,gBAAgB;IAAhBA,gBAAgB,GAAAqC,qBAAA,cAAGpC,uBAAuB,GAAAoC,qBAAA;IAAAC,qBAAA,GAGlCN,IAAA,CAFRlC,gBAAgB;IAAhBA,gBAAgB,GAAAwC,qBAAA,cAAGvC,uBAAuB,GAAAuC,qBAAA;IAAAC,qBAAA,GAElCP,IAAA,CADRpC,kBAAkB;IAAlBA,kBAAkB,GAAA2C,qBAAA,cAAG1C,yBAAA,GAAA0C,qBAAA;EAGvBpC,KAAK,CAACqC,SAAS,CAAC,YAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,OAAOlC,SAAS;IAClB;IAEA,IAAIwB,WAAW,EAAE;MACf,OAAOxB,SAAS;IAClB;IAEA,IAAI0B,OAAO,KAAK,KAAK,IAAIN,eAAe,CAAChB,MAAM,EAAE;MAC/C+B,OAAO,CAACC,KAAK,CACX,CACE,6KAA6K,EAC7K,uFAAuF,EACvF,4DAA4D,CAC7D,CACEC,IAAI,CAAC,IAAI,CAAC,CACVC,IAAI,EAAE,CACV;IACH;IAEA,IAAMC,OAAO,GAAGC,MAAM,EAAE;IAExB,IAAId,OAAO,KAAK,KAAK,EAAE;MACrBN,eAAe,CAACqB,IAAI,CAACF,OAAO,CAAC;IAC/B;IAEA,OAAO,YAAM;MACX,IAAM/B,KAAK,GAAGY,eAAe,CAACsB,OAAO,CAACH,OAAO,CAAC;MAE9C,IAAI/B,KAAK,GAAG,CAAC,CAAC,EAAE;QACdY,eAAe,CAACuB,MAAM,CAACnC,KAAK,EAAE,CAAC,CAAC;MAClC;IACF,CAAC;EACH,CAAC,EAAE,CAACkB,OAAO,EAAEF,WAAW,CAAC,CAAC;EAE1B,IAAAoB,eAAA,GAAkBlD,KAAK,CAACmD,QAAQ,CAAClD,mBAAmB,CAAC;IAAAmD,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAA9CzC,OAAO,GAAA2C,gBAAA;EAKd,IAAME,UAAU,GAAGtD,KAAK,CAACuD,MAAM,CAACvB,OAAO,CAAC;EACxC,IAAMwB,SAAS,GAAGxD,KAAK,CAACuD,MAAM,CAACtB,MAAM,CAAC;EACtC,IAAMwB,mBAAmB,GAAGzD,KAAK,CAACuD,MAAM,CAAC1D,gBAAgB,CAAC;EAC1D,IAAM6D,mBAAmB,GAAG1D,KAAK,CAACuD,MAAM,CAAC5D,gBAAgB,CAAC;EAC1D,IAAMgE,qBAAqB,GAAG3D,KAAK,CAACuD,MAAM,CAAC9D,kBAAkB,CAAC;EAE9DO,KAAK,CAACqC,SAAS,CAAC,YAAM;IACpBiB,UAAU,CAACM,OAAO,GAAG5B,OAAO;IAC5BwB,SAAS,CAACI,OAAO,GAAG3B,MAAM;IAC1BwB,mBAAmB,CAACG,OAAO,GAAG/D,gBAAgB;IAC9C6D,mBAAmB,CAACE,OAAO,GAAGjE,gBAAgB;IAC9CgE,qBAAqB,CAACC,OAAO,GAAGnE,kBAAkB;EACpD,CAAC,CAAC;EAEF,IAAMoE,MAAM,GAAG7D,KAAK,CAAC8D,UAAU,CAAC5D,aAAa,CAAC;EAE9C,IAAM6D,eAAe,GAAG/D,KAAK,CAACgE,WAAW,CAAC,YAAM;IAC9C,IAAIC,KAA8B;IAElC,IAAIX,UAAU,CAACM,OAAO,EAAE;MAAA,IAAAM,KAAA;MACtB,IAAMC,QAAQ,IAAAD,KAAA,GACZL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEM,QAAQ,YAAAD,KAAA,GACf,OAAOE,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,QAAQ,GAAG7D,SAAU;MAE/D,IAAM+D,IAAI,GAAGF,QAAQ,GAAGA,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM,GAAGjE,SAAS;MAEvE,IAAI+D,IAAI,EAAE;QACRJ,KAAK,GAAGR,mBAAmB,CAACG,OAAO,CAACS,IAAI,EAAEb,SAAS,CAACI,OAAO,CAAC;MAC9D;IACF;IAEA,IAAMY,QAAQ,GAAG;MACf/C,IAAI,WAAJA,IAAIA,CAACgD,WAAsD,EAAE;QAC3D,OAAOnD,OAAO,CAACC,OAAO,CAACkD,WAAW,GAAGA,WAAW,CAACR,KAAK,CAAC,GAAGA,KAAK,CAAC;MAClE,CAAC;MACDS,KAAK,WAALA,MAAKA,CAAA,EAAG;QACN,OAAOF,QAAQ;MACjB;IACF,CAAC;IAED,OAAOA,QAAQ;EAEjB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMG,gBAAgB,GAAG3E,KAAK,CAACuD,MAAM,CAAqBjD,SAAS,CAAC;EACpE,IAAMsE,gBAAgB,GAAG5E,KAAK,CAACuD,MAAM,CAA8BjD,SAAS,CAAC;EAC7E,IAAMuE,sBAAsB,GAAG7E,KAAK,CAACuD,MAAM,CAAqBjD,SAAS,CAAC;EAE1EN,KAAK,CAACqC,SAAS,CAAC,YAAM;IACpBsC,gBAAgB,CAACf,OAAO,GAAGnD,OAAO,CAACK,KAAK;IAExC,OAAOL,OAAO,CAACqE,MAAM,CAAC,YAAM;MAAA,IAAAC,qBAAA;MAC1B,IAAMC,UAAU,GAAGpD,GAAG,CAACgC,OAAO;MAE9B,IAAI,CAACoB,UAAU,IAAI,CAAChD,OAAO,EAAE;QAC3B;MACF;MAEA,IAAAiD,OAAA,GAAqBb,MAAM;QAAnBD,QAAA,GAAAc,OAAA,CAAAd,QAAA;MAER,IAAME,IAAI,GAAGF,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACI,MAAM;MAChD,IAAMzD,KAAK,GAAGL,OAAO,CAACK,KAAK;MAE3B,IAAMoE,aAAa,IAAAH,qBAAA,GAAGJ,gBAAgB,CAACf,OAAO,YAAAmB,qBAAA,GAAI,CAAC;MAEnDJ,gBAAgB,CAACf,OAAO,GAAG9C,KAAK;MAChC+D,sBAAsB,CAACjB,OAAO,GAAGS,IAAI;MAKrC,IAAMc,MAAM,GAAG1E,OAAO,CAAC2E,GAAG,CAACtE,KAAK,CAAC;MAEjC,IAAI,CAAAqE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEd,IAAI,MAAKA,IAAI,IAAIc,MAAM,aAANA,MAAM,eAANA,MAAM,CAAElE,KAAK,EAAE;QAC1C+D,UAAU,CAACK,SAAS,CAACF,MAAM,CAAClE,KAAK,CAAC;QAClC;MACF;MAEA,IAAMA,KAAK,GAAGwC,mBAAmB,CAACG,OAAO,CAACS,IAAI,EAAEb,SAAS,CAACI,OAAO,CAAC;MAIlE,IAAI3C,KAAK,EAAE;QAGT,IAAMqE,SAAS,GAAGN,UAAU,CAACO,YAAY,EAAE;QAE3C,IAAItE,KAAK,CAACN,MAAM,CAAC6E,IAAI,CAAE,UAAAC,CAAC;UAAA,OAAK,EAACH,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEI,UAAU,CAACC,QAAQ,CAACF,CAAC,CAACG,IAAI,CAAC;QAAA,EAAC,EAAE;UACrEnD,OAAO,CAACoD,IAAI,CACV,0SAA0S,CAC3S;UACD;QACF;QAEA,IAAI/E,KAAK,GAAGoE,aAAa,EAAE;UACzB,IAAMY,MAAM,GAAGnC,qBAAqB,CAACC,OAAO,CAC1C3C,KAAK,EACLuC,SAAS,CAACI,OAAO,CAClB;UAED,IAAIkC,MAAM,KAAKxF,SAAS,EAAE;YACxB,IAAI;cACF0E,UAAU,CAACe,QAAQ,CAACD,MAAM,CAAC;YAC7B,CAAC,CAAC,OAAOE,CAAC,EAAE;cAGVvD,OAAO,CAACoD,IAAI,CACT,qDAAoDxB,IAAK,MACxD,OAAO2B,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,SAAS,IAAIA,CAAC,GAChDA,CAAC,CAACC,OAAO,GACTD,CACL,EAAC,CACH;YACH;UACF,CAAC,MAAM;YACLhB,UAAU,CAACK,SAAS,CAACpE,KAAK,CAAC;UAC7B;QACF,CAAC,MAAM;UACL+D,UAAU,CAACK,SAAS,CAACpE,KAAK,CAAC;QAC7B;MACF,CAAC,MAAM;QAEL+D,UAAU,CAACK,SAAS,CAACpE,KAAK,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACe,OAAO,EAAEvB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3B5B,KAAK,CAACqC,SAAS,CAAC,YAAM;IAAA,IAAA6D,YAAA;IACpB,IAAI,CAAClE,OAAO,EAAE;MACZ;IACF;IAEA,IAAMmE,eAAe,GAAG,SAAlBA,eAAeA,CACnBC,KAA0C,EAC1CnF,KAAsB,EACX;MAGX,IAAImF,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE/B,IAAI,EAAE;QACf,IAAMgC,YAAY,GAAG5C,mBAAmB,CAACG,OAAO,CAC9CwC,KAAK,CAAC/B,IAAI,EACVb,SAAS,CAACI,OAAO,CAClB;QAED,IAAIyC,YAAY,EAAE;UAChB,IAAMC,YAAY,GAAG9G,gBAAgB,CAAC6G,YAAY,CAAC;UAEnD,IACEC,YAAY,IACZA,YAAY,CAACV,IAAI,KAAKQ,KAAK,CAACR,IAAI,IAChC7F,OAAO,CAACuG,YAAY,CAACC,MAAM,EAAEH,KAAK,CAACG,MAAM,CAAC,EAC1C;YACA,OAAOH,KAAK,CAAC/B,IAAI;UACnB;QACF;MACF;MAEA,OAAOX,mBAAmB,CAACE,OAAO,CAAC3C,KAAK,EAAEuC,SAAS,CAACI,OAAO,CAAC;IAC9D,CAAC;IAED,IAAIhC,GAAG,CAACgC,OAAO,EAAE;MAGf,IAAM3C,KAAK,GAAGW,GAAG,CAACgC,OAAO,CAAC2B,YAAY,EAAE;MAExC,IAAItE,KAAK,EAAE;QACT,IAAMmF,KAAK,GAAG5G,gBAAgB,CAACyB,KAAK,CAAC;QACrC,IAAMoD,IAAI,GAAG8B,eAAe,CAACC,KAAK,EAAEnF,KAAK,CAAC;QAE1C,IAAI2D,gBAAgB,CAAChB,OAAO,KAAKtD,SAAS,EAAE;UAC1CsE,gBAAgB,CAAChB,OAAO,GAAG3C,KAAK;QAClC;QAEAR,OAAO,CAAC+F,OAAO,CAAC;UAAEnC,IAAI,EAAJA,IAAI;UAAEpD,KAAA,EAAAA;QAAM,CAAC,CAAC;MAClC;IACF;IAEA,IAAMwF,aAAa;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAChC,IAAM3B,UAAU,GAAGpD,GAAG,CAACgC,OAAO;QAE9B,IAAI,CAACoB,UAAU,IAAI,CAAChD,OAAO,EAAE;UAC3B;QACF;QAEA,IAAM4E,aAAa,GAAGhC,gBAAgB,CAAChB,OAAO;QAC9C,IAAM3C,KAAK,GAAG+D,UAAU,CAACO,YAAY,EAAE;QAGvC,IAAI,CAACtE,KAAK,EAAE;UACV;QACF;QAEA,IAAM4F,WAAW,GAAGhC,sBAAsB,CAACjB,OAAO;QAClD,IAAMwC,KAAK,GAAG5G,gBAAgB,CAACyB,KAAK,CAAC;QACrC,IAAMoD,IAAI,GAAG8B,eAAe,CAACC,KAAK,EAAEnF,KAAK,CAAC;QAE1C2D,gBAAgB,CAAChB,OAAO,GAAG3C,KAAK;QAChC4D,sBAAsB,CAACjB,OAAO,GAAGtD,SAAS;QAM1C,IAAAwG,mBAAA,GAA6C3G,kBAAiB,CAC5DyG,aAAa,EACb3F,KAAK,CACN;UAAA8F,mBAAA,GAAA1D,cAAA,CAAAyD,mBAAA;UAHME,oBAAoB,GAAAD,mBAAA;UAAEE,YAAY,GAAAF,mBAAA;QAKzC,IACEC,oBAAoB,IACpBC,YAAY,IAGZ5C,IAAI,KAAKwC,WAAW,EACpB;UACA,IAAMK,YAAY,GAChB,CAACD,YAAY,CAACxG,OAAO,GACjBwG,YAAY,CAACxG,OAAO,CAACC,MAAM,GAC3BuG,YAAY,CAACtG,MAAM,CAACD,MAAM,KAC7BsG,oBAAoB,CAACvG,OAAO,GACzBuG,oBAAoB,CAACvG,OAAO,CAACC,MAAM,GACnCsG,oBAAoB,CAACrG,MAAM,CAACD,MAAM,CAAC;UAEzC,IAAIwG,YAAY,GAAG,CAAC,EAAE;YAGpBzG,OAAO,CAACsC,IAAI,CAAC;cAAEsB,IAAI,EAAJA,IAAI;cAAEpD,KAAA,EAAAA;YAAM,CAAC,CAAC;UAC/B,CAAC,MAAM,IAAIiG,YAAY,GAAG,CAAC,EAAE;YAG3B,IAAMC,SAAS,GAAG1G,OAAO,CAAC2G,SAAS,CAAC;cAAE/C,IAAA,EAAAA;YAAK,CAAC,CAAC;YAC7C,IAAMgD,YAAY,GAAG5G,OAAO,CAACK,KAAK;YAElC,IAAI;cACF,IACEqG,SAAS,KAAK,CAAC,CAAC,IAChBA,SAAS,GAAGE,YAAY,IAExB5G,OAAO,CAAC2E,GAAG,CAAC+B,SAAS,GAAGE,YAAY,CAAC,EACrC;gBAEA,MAAM5G,OAAO,CAAC6G,EAAE,CAACH,SAAS,GAAGE,YAAY,CAAC;cAC5C,CAAC,MAAM;gBAIL,MAAM5G,OAAO,CAAC6G,EAAE,CAACJ,YAAY,CAAC;cAChC;cAGAzG,OAAO,CAAC+F,OAAO,CAAC;gBAAEnC,IAAI,EAAJA,IAAI;gBAAEpD,KAAA,EAAAA;cAAM,CAAC,CAAC;YAClC,CAAC,CAAC,OAAO+E,CAAC,EAAE,CACV;UAEJ,CAAC,MAAM;YAELvF,OAAO,CAAC+F,OAAO,CAAC;cAAEnC,IAAI,EAAJA,IAAI;cAAEpD,KAAA,EAAAA;YAAM,CAAC,CAAC;UAClC;QACF,CAAC,MAAM;UAGLR,OAAO,CAAC+F,OAAO,CAAC;YAAEnC,IAAI,EAAJA,IAAI;YAAEpD,KAAA,EAAAA;UAAM,CAAC,CAAC;QAClC;MACF,CAAC;MAAA,gBAtFKwF,aAAaA,CAAA;QAAA,OAAAC,KAAA,CAAAa,KAAA,OAAAC,SAAA;MAAA;IAAA,GAsFlB;IAKD,QAAAtB,YAAA,GAAOtE,GAAG,CAACgC,OAAO,cAAAsC,YAAA,uBAAXA,YAAA,CAAauB,WAAW,CAAC,OAAO,EAAEtG,MAAM,CAACsF,aAAa,CAAC,CAAC;EACjE,CAAC,EAAE,CAACzE,OAAO,EAAEvB,OAAO,EAAEmB,GAAG,CAAC,CAAC;EAE3B,OAAO;IACLmC,eAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}