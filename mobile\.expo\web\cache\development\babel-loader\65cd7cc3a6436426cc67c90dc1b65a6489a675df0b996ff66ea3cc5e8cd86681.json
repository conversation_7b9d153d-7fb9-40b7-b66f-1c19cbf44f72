{"ast": null, "code": "import * as React from 'react';\nimport HeaderHeightContext from \"./HeaderHeightContext\";\nexport default function useHeaderHeight() {\n  var height = React.useContext(HeaderHeightContext);\n  if (height === undefined) {\n    throw new Error(\"Couldn't find the header height. Are you inside a screen in a navigator with a header?\");\n  }\n  return height;\n}", "map": {"version": 3, "names": ["React", "HeaderHeightContext", "useHeaderHeight", "height", "useContext", "undefined", "Error"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\elements\\src\\Header\\useHeaderHeight.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport HeaderHeightContext from './HeaderHeightContext';\n\nexport default function useHeaderHeight() {\n  const height = React.useContext(HeaderHeightContext);\n\n  if (height === undefined) {\n    throw new Error(\n      \"Couldn't find the header height. Are you inside a screen in a navigator with a header?\"\n    );\n  }\n\n  return height;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,mBAAmB;AAE1B,eAAe,SAASC,eAAeA,CAAA,EAAG;EACxC,IAAMC,MAAM,GAAGH,KAAK,CAACI,UAAU,CAACH,mBAAmB,CAAC;EAEpD,IAAIE,MAAM,KAAKE,SAAS,EAAE;IACxB,MAAM,IAAIC,KAAK,CACb,wFAAwF,CACzF;EACH;EAEA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}