{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { useNavigation } from '@react-navigation/native';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar RegisterScreen = function RegisterScreen() {\n  var _useState = useState(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    email = _useState2[0],\n    setEmail = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    username = _useState4[0],\n    setUsername = _useState4[1];\n  var _useState5 = useState(''),\n    _useState6 = _slicedToArray(_useState5, 2),\n    password = _useState6[0],\n    setPassword = _useState6[1];\n  var _useState7 = useState(''),\n    _useState8 = _slicedToArray(_useState7, 2),\n    confirmPassword = _useState8[0],\n    setConfirmPassword = _useState8[1];\n  var navigation = useNavigation();\n  var handleRegister = function handleRegister() {\n    console.log('Registration attempt with:', email, username, password);\n    navigation.navigate('Login');\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"Register\"\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Email\",\n      value: email,\n      onChangeText: setEmail,\n      keyboardType: \"email-address\",\n      autoCapitalize: \"none\"\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Username\",\n      value: username,\n      onChangeText: setUsername,\n      autoCapitalize: \"none\"\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Password\",\n      value: password,\n      onChangeText: setPassword,\n      secureTextEntry: true\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Confirm Password\",\n      value: confirmPassword,\n      onChangeText: setConfirmPassword,\n      secureTextEntry: true\n    }), _jsx(TouchableOpacity, {\n      style: styles.registerButton,\n      onPress: handleRegister,\n      children: _jsx(Text, {\n        style: styles.registerButtonText,\n        children: \"Register\"\n      })\n    }), _jsx(TouchableOpacity, {\n      onPress: function onPress() {\n        return navigation.navigate('Login');\n      },\n      children: _jsx(Text, {\n        style: styles.loginLink,\n        children: \"Already have an account? Login here\"\n      })\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 30,\n    textAlign: 'center'\n  },\n  input: {\n    backgroundColor: 'white',\n    padding: 15,\n    borderRadius: 8,\n    marginBottom: 15,\n    borderWidth: 1,\n    borderColor: '#ddd'\n  },\n  registerButton: {\n    backgroundColor: '#28a745',\n    padding: 15,\n    borderRadius: 8,\n    alignItems: 'center',\n    marginBottom: 20\n  },\n  registerButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600'\n  },\n  loginLink: {\n    textAlign: 'center',\n    color: '#007bff',\n    fontSize: 16\n  }\n});\nexport default RegisterScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "StyleSheet", "TextInput", "TouchableOpacity", "useNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "RegisterScreen", "_useState", "_useState2", "_slicedToArray", "email", "setEmail", "_useState3", "_useState4", "username", "setUsername", "_useState5", "_useState6", "password", "setPassword", "_useState7", "_useState8", "confirmPassword", "setConfirmPassword", "navigation", "handleRegister", "console", "log", "navigate", "style", "styles", "container", "children", "title", "input", "placeholder", "value", "onChangeText", "keyboardType", "autoCapitalize", "secureTextEntry", "registerButton", "onPress", "registerButtonText", "loginLink", "create", "flex", "justifyContent", "padding", "backgroundColor", "fontSize", "fontWeight", "marginBottom", "textAlign", "borderRadius", "borderWidth", "borderColor", "alignItems", "color"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/screens/RegisterScreen.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\n\nconst RegisterScreen = () => {\n  const [email, setEmail] = useState('');\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const navigation = useNavigation();\n\n  const handleRegister = () => {\n    // Registration logic will be implemented later\n    console.log('Registration attempt with:', email, username, password);\n    // For now, just navigate to Login\n    navigation.navigate('Login');\n  };\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Register</Text>\n      \n      <TextInput\n        style={styles.input}\n        placeholder=\"Email\"\n        value={email}\n        onChangeText={setEmail}\n        keyboardType=\"email-address\"\n        autoCapitalize=\"none\"\n      />\n      \n      <TextInput\n        style={styles.input}\n        placeholder=\"Username\"\n        value={username}\n        onChangeText={setUsername}\n        autoCapitalize=\"none\"\n      />\n      \n      <TextInput\n        style={styles.input}\n        placeholder=\"Password\"\n        value={password}\n        onChangeText={setPassword}\n        secureTextEntry\n      />\n      \n      <TextInput\n        style={styles.input}\n        placeholder=\"Confirm Password\"\n        value={confirmPassword}\n        onChangeText={setConfirmPassword}\n        secureTextEntry\n      />\n      \n      <TouchableOpacity style={styles.registerButton} onPress={handleRegister}>\n        <Text style={styles.registerButtonText}>Register</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity onPress={() => navigation.navigate('Login')}>\n        <Text style={styles.loginLink}>\n          Already have an account? Login here\n        </Text>\n      </TouchableOpacity>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 30,\n    textAlign: 'center',\n  },\n  input: {\n    backgroundColor: 'white',\n    padding: 15,\n    borderRadius: 8,\n    marginBottom: 15,\n    borderWidth: 1,\n    borderColor: '#ddd',\n  },\n  registerButton: {\n    backgroundColor: '#28a745',\n    padding: 15,\n    borderRadius: 8,\n    alignItems: 'center',\n    marginBottom: 20,\n  },\n  registerButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n  loginLink: {\n    textAlign: 'center',\n    color: '#007bff',\n    fontSize: 16,\n  },\n});\n\nexport default RegisterScreen;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAExC,SAASC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;EAC3B,IAAAC,SAAA,GAA0BZ,QAAQ,CAAC,EAAE,CAAC;IAAAa,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA/BG,KAAK,GAAAF,UAAA;IAAEG,QAAQ,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAAgCjB,QAAQ,CAAC,EAAE,CAAC;IAAAkB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAgCrB,QAAQ,CAAC,EAAE,CAAC;IAAAsB,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAA8CzB,QAAQ,CAAC,EAAE,CAAC;IAAA0B,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAAnDE,eAAe,GAAAD,UAAA;IAAEE,kBAAkB,GAAAF,UAAA;EAC1C,IAAMG,UAAU,GAAGvB,aAAa,CAAC,CAAC;EAElC,IAAMwB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAE3BC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEjB,KAAK,EAAEI,QAAQ,EAAEI,QAAQ,CAAC;IAEpEM,UAAU,CAACI,QAAQ,CAAC,OAAO,CAAC;EAC9B,CAAC;EAED,OACEvB,KAAA,CAACT,IAAI;IAACiC,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5B7B,IAAA,CAACN,IAAI;MAACgC,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAQ,CAAM,CAAC,EAE1C7B,IAAA,CAACJ,SAAS;MACR8B,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,OAAO;MACnBC,KAAK,EAAE1B,KAAM;MACb2B,YAAY,EAAE1B,QAAS;MACvB2B,YAAY,EAAC,eAAe;MAC5BC,cAAc,EAAC;IAAM,CACtB,CAAC,EAEFpC,IAAA,CAACJ,SAAS;MACR8B,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,UAAU;MACtBC,KAAK,EAAEtB,QAAS;MAChBuB,YAAY,EAAEtB,WAAY;MAC1BwB,cAAc,EAAC;IAAM,CACtB,CAAC,EAEFpC,IAAA,CAACJ,SAAS;MACR8B,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,UAAU;MACtBC,KAAK,EAAElB,QAAS;MAChBmB,YAAY,EAAElB,WAAY;MAC1BqB,eAAe;IAAA,CAChB,CAAC,EAEFrC,IAAA,CAACJ,SAAS;MACR8B,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,kBAAkB;MAC9BC,KAAK,EAAEd,eAAgB;MACvBe,YAAY,EAAEd,kBAAmB;MACjCiB,eAAe;IAAA,CAChB,CAAC,EAEFrC,IAAA,CAACH,gBAAgB;MAAC6B,KAAK,EAAEC,MAAM,CAACW,cAAe;MAACC,OAAO,EAAEjB,cAAe;MAAAO,QAAA,EACtE7B,IAAA,CAACN,IAAI;QAACgC,KAAK,EAAEC,MAAM,CAACa,kBAAmB;QAAAX,QAAA,EAAC;MAAQ,CAAM;IAAC,CACvC,CAAC,EAEnB7B,IAAA,CAACH,gBAAgB;MAAC0C,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQlB,UAAU,CAACI,QAAQ,CAAC,OAAO,CAAC;MAAA,CAAC;MAAAI,QAAA,EAC5D7B,IAAA,CAACN,IAAI;QAACgC,KAAK,EAAEC,MAAM,CAACc,SAAU;QAAAZ,QAAA,EAAC;MAE/B,CAAM;IAAC,CACS,CAAC;EAAA,CACf,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGhC,UAAU,CAAC+C,MAAM,CAAC;EAC/Bd,SAAS,EAAE;IACTe,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDhB,KAAK,EAAE;IACLiB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDnB,KAAK,EAAE;IACLe,eAAe,EAAE,OAAO;IACxBD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfF,YAAY,EAAE,EAAE;IAChBG,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDf,cAAc,EAAE;IACdQ,eAAe,EAAE,SAAS;IAC1BD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfG,UAAU,EAAE,QAAQ;IACpBL,YAAY,EAAE;EAChB,CAAC;EACDT,kBAAkB,EAAE;IAClBe,KAAK,EAAE,OAAO;IACdR,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDP,SAAS,EAAE;IACTS,SAAS,EAAE,QAAQ;IACnBK,KAAK,EAAE,SAAS;IAChBR,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,eAAe5C,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}