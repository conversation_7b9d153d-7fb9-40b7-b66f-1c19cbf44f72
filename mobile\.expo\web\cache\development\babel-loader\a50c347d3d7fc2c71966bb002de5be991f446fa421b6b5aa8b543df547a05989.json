{"ast": null, "code": "import { getPathFromState, NavigationContainerRefContext, NavigationHelpersContext } from '@react-navigation/core';\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport LinkingContext from \"./LinkingContext\";\nimport useLinkTo from \"./useLinkTo\";\nvar _getStateFromParams = function getStateFromParams(params) {\n  if (params !== null && params !== void 0 && params.state) {\n    return params.state;\n  }\n  if (params !== null && params !== void 0 && params.screen) {\n    return {\n      routes: [{\n        name: params.screen,\n        params: params.params,\n        state: params.screen ? _getStateFromParams(params.params) : undefined\n      }]\n    };\n  }\n  return undefined;\n};\nexport default function useLinkProps(_ref) {\n  var _ref2;\n  var to = _ref.to,\n    action = _ref.action;\n  var root = React.useContext(NavigationContainerRefContext);\n  var navigation = React.useContext(NavigationHelpersContext);\n  var _React$useContext = React.useContext(LinkingContext),\n    options = _React$useContext.options;\n  var linkTo = useLinkTo();\n  var onPress = function onPress(e) {\n    var _e$currentTarget;\n    var shouldHandle = false;\n    if (Platform.OS !== 'web' || !e) {\n      shouldHandle = e ? !e.defaultPrevented : true;\n    } else if (!e.defaultPrevented && !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && (e.button == null || e.button === 0) && [undefined, null, '', 'self'].includes((_e$currentTarget = e.currentTarget) === null || _e$currentTarget === void 0 ? void 0 : _e$currentTarget.target)) {\n      e.preventDefault();\n      shouldHandle = true;\n    }\n    if (shouldHandle) {\n      if (action) {\n        if (navigation) {\n          navigation.dispatch(action);\n        } else if (root) {\n          root.dispatch(action);\n        } else {\n          throw new Error(\"Couldn't find a navigation object. Is your component inside NavigationContainer?\");\n        }\n      } else {\n        linkTo(to);\n      }\n    }\n  };\n  var getPathFromStateHelper = (_ref2 = options === null || options === void 0 ? void 0 : options.getPathFromState) != null ? _ref2 : getPathFromState;\n  var href = typeof to === 'string' ? to : getPathFromStateHelper({\n    routes: [{\n      name: to.screen,\n      params: to.params,\n      state: _getStateFromParams(to.params)\n    }]\n  }, options === null || options === void 0 ? void 0 : options.config);\n  return {\n    href: href,\n    accessibilityRole: 'link',\n    onPress: onPress\n  };\n}", "map": {"version": 3, "names": ["getPathFromState", "NavigationContainerRefContext", "NavigationHelpersContext", "React", "Platform", "LinkingContext", "useLinkTo", "getStateFromParams", "params", "state", "screen", "routes", "name", "undefined", "useLinkProps", "_ref", "_ref2", "to", "action", "root", "useContext", "navigation", "_React$useContext", "options", "linkTo", "onPress", "e", "_e$currentTarget", "<PERSON><PERSON><PERSON><PERSON>", "OS", "defaultPrevented", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "button", "includes", "currentTarget", "target", "preventDefault", "dispatch", "Error", "getPathFromStateHelper", "href", "config", "accessibilityRole"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native\\src\\useLinkProps.tsx"], "sourcesContent": ["import {\n  getPathFromState,\n  NavigationAction,\n  NavigationContainerRefContext,\n  NavigationHelpersContext,\n  NavigatorScreenParams,\n  ParamListBase,\n} from '@react-navigation/core';\nimport type { NavigationState, PartialState } from '@react-navigation/routers';\nimport * as React from 'react';\nimport { GestureResponderEvent, Platform } from 'react-native';\n\nimport LinkingContext from './LinkingContext';\nimport useLinkTo, { To } from './useLinkTo';\n\ntype Props<ParamList extends ReactNavigation.RootParamList> = {\n  to: To<ParamList>;\n  action?: NavigationAction;\n};\n\nconst getStateFromParams = (\n  params: NavigatorScreenParams<ParamListBase, NavigationState> | undefined\n): PartialState<NavigationState> | NavigationState | undefined => {\n  if (params?.state) {\n    return params.state;\n  }\n\n  if (params?.screen) {\n    return {\n      routes: [\n        {\n          name: params.screen,\n          params: params.params,\n          // @ts-expect-error\n          state: params.screen\n            ? getStateFromParams(\n                params.params as\n                  | NavigatorScreenParams<ParamListBase, NavigationState>\n                  | undefined\n              )\n            : undefined,\n        },\n      ],\n    };\n  }\n\n  return undefined;\n};\n\n/**\n * Hook to get props for an anchor tag so it can work with in page navigation.\n *\n * @param props.to Absolute path to screen (e.g. `/feeds/hot`).\n * @param props.action Optional action to use for in-page navigation. By default, the path is parsed to an action based on linking config.\n */\nexport default function useLinkProps<\n  ParamList extends ReactNavigation.RootParamList\n>({ to, action }: Props<ParamList>) {\n  const root = React.useContext(NavigationContainerRefContext);\n  const navigation = React.useContext(NavigationHelpersContext);\n  const { options } = React.useContext(LinkingContext);\n  const linkTo = useLinkTo<ParamList>();\n\n  const onPress = (\n    e?: React.MouseEvent<HTMLAnchorElement, MouseEvent> | GestureResponderEvent\n  ) => {\n    let shouldHandle = false;\n\n    if (Platform.OS !== 'web' || !e) {\n      shouldHandle = e ? !e.defaultPrevented : true;\n    } else if (\n      !e.defaultPrevented && // onPress prevented default\n      // @ts-expect-error: these properties exist on web, but not in React Native\n      !(e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) && // ignore clicks with modifier keys\n      // @ts-expect-error: these properties exist on web, but not in React Native\n      (e.button == null || e.button === 0) && // ignore everything but left clicks\n      // @ts-expect-error: these properties exist on web, but not in React Native\n      [undefined, null, '', 'self'].includes(e.currentTarget?.target) // let browser handle \"target=_blank\" etc.\n    ) {\n      e.preventDefault();\n      shouldHandle = true;\n    }\n\n    if (shouldHandle) {\n      if (action) {\n        if (navigation) {\n          navigation.dispatch(action);\n        } else if (root) {\n          root.dispatch(action);\n        } else {\n          throw new Error(\n            \"Couldn't find a navigation object. Is your component inside NavigationContainer?\"\n          );\n        }\n      } else {\n        linkTo(to);\n      }\n    }\n  };\n\n  const getPathFromStateHelper = options?.getPathFromState ?? getPathFromState;\n\n  const href =\n    typeof to === 'string'\n      ? to\n      : getPathFromStateHelper(\n          {\n            routes: [\n              {\n                name: to.screen,\n                // @ts-expect-error\n                params: to.params,\n                // @ts-expect-error\n                state: getStateFromParams(to.params),\n              },\n            ],\n          },\n          options?.config\n        );\n\n  return {\n    href,\n    accessibilityRole: 'link' as const,\n    onPress,\n  };\n}\n"], "mappings": "AAAA,SACEA,gBAAgB,EAEhBC,6BAA6B,EAC7BC,wBAAwB,QAGnB,wBAAwB;AAE/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAG9B,OAAOC,cAAc;AACrB,OAAOC,SAAS;AAOhB,IAAMC,mBAAkB,GACtB,SADIA,kBAAkBA,CACtBC,MAAyE,EACT;EAChE,IAAIA,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEC,KAAK,EAAE;IACjB,OAAOD,MAAM,CAACC,KAAK;EACrB;EAEA,IAAID,MAAM,aAANA,MAAM,eAANA,MAAM,CAAEE,MAAM,EAAE;IAClB,OAAO;MACLC,MAAM,EAAE,CACN;QACEC,IAAI,EAAEJ,MAAM,CAACE,MAAM;QACnBF,MAAM,EAAEA,MAAM,CAACA,MAAM;QAErBC,KAAK,EAAED,MAAM,CAACE,MAAM,GAChBH,mBAAkB,CAChBC,MAAM,CAACA,MAAM,CAGd,GACDK;MACN,CAAC;IAEL,CAAC;EACH;EAEA,OAAOA,SAAS;AAClB,CAAC;AAQD,eAAe,SAASC,YAAYA,CAAAC,IAAA,EAEA;EAAA,IAAAC,KAAA;EAAA,IAAhCC,EAAE,GAA4BF,IAAA,CAA9BE,EAAE;IAAEC,MAAA,GAA0BH,IAAA,CAA1BG,MAAA;EACN,IAAMC,IAAI,GAAGhB,KAAK,CAACiB,UAAU,CAACnB,6BAA6B,CAAC;EAC5D,IAAMoB,UAAU,GAAGlB,KAAK,CAACiB,UAAU,CAAClB,wBAAwB,CAAC;EAC7D,IAAAoB,iBAAA,GAAoBnB,KAAK,CAACiB,UAAU,CAACf,cAAc,CAAC;IAA5CkB,OAAA,GAAAD,iBAAA,CAAAC,OAAA;EACR,IAAMC,MAAM,GAAGlB,SAAS,EAAa;EAErC,IAAMmB,OAAO,GACX,SADIA,OAAOA,CACXC,CAA2E,EACxE;IAAA,IAAAC,gBAAA;IACH,IAAIC,YAAY,GAAG,KAAK;IAExB,IAAIxB,QAAQ,CAACyB,EAAE,KAAK,KAAK,IAAI,CAACH,CAAC,EAAE;MAC/BE,YAAY,GAAGF,CAAC,GAAG,CAACA,CAAC,CAACI,gBAAgB,GAAG,IAAI;IAC/C,CAAC,MAAM,IACL,CAACJ,CAAC,CAACI,gBAAgB,IAEnB,EAAEJ,CAAC,CAACK,OAAO,IAAIL,CAAC,CAACM,MAAM,IAAIN,CAAC,CAACO,OAAO,IAAIP,CAAC,CAACQ,QAAQ,CAAC,KAElDR,CAAC,CAACS,MAAM,IAAI,IAAI,IAAIT,CAAC,CAACS,MAAM,KAAK,CAAC,CAAC,IAEpC,CAACtB,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAACuB,QAAQ,EAAAT,gBAAA,GAACD,CAAC,CAACW,aAAa,cAAAV,gBAAA,uBAAfA,gBAAA,CAAiBW,MAAM,CAAC,EAC/D;MACAZ,CAAC,CAACa,cAAc,EAAE;MAClBX,YAAY,GAAG,IAAI;IACrB;IAEA,IAAIA,YAAY,EAAE;MAChB,IAAIV,MAAM,EAAE;QACV,IAAIG,UAAU,EAAE;UACdA,UAAU,CAACmB,QAAQ,CAACtB,MAAM,CAAC;QAC7B,CAAC,MAAM,IAAIC,IAAI,EAAE;UACfA,IAAI,CAACqB,QAAQ,CAACtB,MAAM,CAAC;QACvB,CAAC,MAAM;UACL,MAAM,IAAIuB,KAAK,CACb,kFAAkF,CACnF;QACH;MACF,CAAC,MAAM;QACLjB,MAAM,CAACP,EAAE,CAAC;MACZ;IACF;EACF,CAAC;EAED,IAAMyB,sBAAsB,IAAA1B,KAAA,GAAGO,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEvB,gBAAgB,YAAAgB,KAAA,GAAIhB,gBAAgB;EAE5E,IAAM2C,IAAI,GACR,OAAO1B,EAAE,KAAK,QAAQ,GAClBA,EAAE,GACFyB,sBAAsB,CACpB;IACE/B,MAAM,EAAE,CACN;MACEC,IAAI,EAAEK,EAAE,CAACP,MAAM;MAEfF,MAAM,EAAES,EAAE,CAACT,MAAM;MAEjBC,KAAK,EAAEF,mBAAkB,CAACU,EAAE,CAACT,MAAM;IACrC,CAAC;EAEL,CAAC,EACDe,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEqB,MAAM,CAChB;EAEP,OAAO;IACLD,IAAI,EAAJA,IAAI;IACJE,iBAAiB,EAAE,MAAe;IAClCpB,OAAA,EAAAA;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}