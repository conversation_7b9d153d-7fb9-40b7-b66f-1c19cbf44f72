{"ast": null, "code": "import { getModality } from \"../modality\";\nimport useEvent from \"../useEvent\";\nimport useLayoutEffect from \"../useLayoutEffect\";\nvar emptyObject = {};\nvar opts = {\n  passive: true\n};\nvar lockEventType = 'react-gui:hover:lock';\nvar unlockEventType = 'react-gui:hover:unlock';\nvar supportsPointerEvent = function supportsPointerEvent() {\n  return !!(typeof window !== 'undefined' && window.PointerEvent != null);\n};\nfunction dispatchCustomEvent(target, type, payload) {\n  var event = document.createEvent('CustomEvent');\n  var _ref = payload || emptyObject,\n    _ref$bubbles = _ref.bubbles,\n    bubbles = _ref$bubbles === void 0 ? true : _ref$bubbles,\n    _ref$cancelable = _ref.cancelable,\n    cancelable = _ref$cancelable === void 0 ? true : _ref$cancelable,\n    detail = _ref.detail;\n  event.initCustomEvent(type, bubbles, cancelable, detail);\n  target.dispatchEvent(event);\n}\nfunction getPointerType(event) {\n  var pointerType = event.pointerType;\n  return pointerType != null ? pointerType : getModality();\n}\nexport default function useHover(targetRef, config) {\n  var contain = config.contain,\n    disabled = config.disabled,\n    onHoverStart = config.onHoverStart,\n    onHoverChange = config.onHoverChange,\n    onHoverUpdate = config.onHoverUpdate,\n    onHoverEnd = config.onHoverEnd;\n  var canUsePE = supportsPointerEvent();\n  var addMoveListener = useEvent(canUsePE ? 'pointermove' : 'mousemove', opts);\n  var addEnterListener = useEvent(canUsePE ? 'pointerenter' : 'mouseenter', opts);\n  var addLeaveListener = useEvent(canUsePE ? 'pointerleave' : 'mouseleave', opts);\n  var addLockListener = useEvent(lockEventType, opts);\n  var addUnlockListener = useEvent(unlockEventType, opts);\n  useLayoutEffect(function () {\n    var target = targetRef.current;\n    if (target !== null) {\n      var hoverEnd = function hoverEnd(e) {\n        if (onHoverEnd != null) {\n          onHoverEnd(e);\n        }\n        if (onHoverChange != null) {\n          onHoverChange(false);\n        }\n        addMoveListener(target, null);\n        addLeaveListener(target, null);\n      };\n      var leaveListener = function leaveListener(e) {\n        var target = targetRef.current;\n        if (target != null && getPointerType(e) !== 'touch') {\n          if (contain) {\n            dispatchCustomEvent(target, unlockEventType);\n          }\n          hoverEnd(e);\n        }\n      };\n      var moveListener = function moveListener(e) {\n        if (getPointerType(e) !== 'touch') {\n          if (onHoverUpdate != null) {\n            if (e.x == null) {\n              e.x = e.clientX;\n            }\n            if (e.y == null) {\n              e.y = e.clientY;\n            }\n            onHoverUpdate(e);\n          }\n        }\n      };\n      var hoverStart = function hoverStart(e) {\n        if (onHoverStart != null) {\n          onHoverStart(e);\n        }\n        if (onHoverChange != null) {\n          onHoverChange(true);\n        }\n        if (onHoverUpdate != null) {\n          addMoveListener(target, !disabled ? moveListener : null);\n        }\n        addLeaveListener(target, !disabled ? leaveListener : null);\n      };\n      var enterListener = function enterListener(e) {\n        var target = targetRef.current;\n        if (target != null && getPointerType(e) !== 'touch') {\n          if (contain) {\n            dispatchCustomEvent(target, lockEventType);\n          }\n          hoverStart(e);\n          var lockListener = function lockListener(lockEvent) {\n            if (lockEvent.target !== target) {\n              hoverEnd(e);\n            }\n          };\n          var unlockListener = function unlockListener(lockEvent) {\n            if (lockEvent.target !== target) {\n              hoverStart(e);\n            }\n          };\n          addLockListener(target, !disabled ? lockListener : null);\n          addUnlockListener(target, !disabled ? unlockListener : null);\n        }\n      };\n      addEnterListener(target, !disabled ? enterListener : null);\n    }\n  }, [addEnterListener, addMoveListener, addLeaveListener, addLockListener, addUnlockListener, contain, disabled, onHoverStart, onHoverChange, onHoverUpdate, onHoverEnd, targetRef]);\n}", "map": {"version": 3, "names": ["getModality", "useEvent", "useLayoutEffect", "emptyObject", "opts", "passive", "lockEventType", "unlockEventType", "supportsPointerEvent", "window", "PointerEvent", "dispatchCustomEvent", "target", "type", "payload", "event", "document", "createEvent", "_ref", "_ref$bubbles", "bubbles", "_ref$cancelable", "cancelable", "detail", "initCustomEvent", "dispatchEvent", "getPointerType", "pointerType", "useHover", "targetRef", "config", "contain", "disabled", "onHoverStart", "onHoverChange", "onHoverUpdate", "onHoverEnd", "canUsePE", "addMoveListener", "addEnterListener", "addLeaveListener", "addLockListener", "addUnlockListener", "current", "hoverEnd", "e", "leaveListener", "moveListener", "x", "clientX", "y", "clientY", "hoverStart", "enterListener", "lockListener", "lockEvent", "unlockListener"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/modules/useHover/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport { getModality } from '../modality';\nimport useEvent from '../useEvent';\nimport useLayoutEffect from '../useLayoutEffect';\n\n/**\n * Types\n */\n\n/**\n * Implementation\n */\n\nvar emptyObject = {};\nvar opts = {\n  passive: true\n};\nvar lockEventType = 'react-gui:hover:lock';\nvar unlockEventType = 'react-gui:hover:unlock';\nvar supportsPointerEvent = () => !!(typeof window !== 'undefined' && window.PointerEvent != null);\nfunction dispatchCustomEvent(target, type, payload) {\n  var event = document.createEvent('CustomEvent');\n  var _ref = payload || emptyObject,\n    _ref$bubbles = _ref.bubbles,\n    bubbles = _ref$bubbles === void 0 ? true : _ref$bubbles,\n    _ref$cancelable = _ref.cancelable,\n    cancelable = _ref$cancelable === void 0 ? true : _ref$cancelable,\n    detail = _ref.detail;\n  event.initCustomEvent(type, bubbles, cancelable, detail);\n  target.dispatchEvent(event);\n}\n\n// This accounts for the non-PointerEvent fallback events.\nfunction getPointerType(event) {\n  var pointerType = event.pointerType;\n  return pointerType != null ? pointerType : getModality();\n}\nexport default function useHover(targetRef, config) {\n  var contain = config.contain,\n    disabled = config.disabled,\n    onHoverStart = config.onHoverStart,\n    onHoverChange = config.onHoverChange,\n    onHoverUpdate = config.onHoverUpdate,\n    onHoverEnd = config.onHoverEnd;\n  var canUsePE = supportsPointerEvent();\n  var addMoveListener = useEvent(canUsePE ? 'pointermove' : 'mousemove', opts);\n  var addEnterListener = useEvent(canUsePE ? 'pointerenter' : 'mouseenter', opts);\n  var addLeaveListener = useEvent(canUsePE ? 'pointerleave' : 'mouseleave', opts);\n  // These custom events are used to implement the \"contain\" prop.\n  var addLockListener = useEvent(lockEventType, opts);\n  var addUnlockListener = useEvent(unlockEventType, opts);\n  useLayoutEffect(() => {\n    var target = targetRef.current;\n    if (target !== null) {\n      /**\n       * End the hover gesture\n       */\n      var hoverEnd = function hoverEnd(e) {\n        if (onHoverEnd != null) {\n          onHoverEnd(e);\n        }\n        if (onHoverChange != null) {\n          onHoverChange(false);\n        }\n        // Remove the listeners once finished.\n        addMoveListener(target, null);\n        addLeaveListener(target, null);\n      };\n\n      /**\n       * Leave element\n       */\n      var leaveListener = function leaveListener(e) {\n        var target = targetRef.current;\n        if (target != null && getPointerType(e) !== 'touch') {\n          if (contain) {\n            dispatchCustomEvent(target, unlockEventType);\n          }\n          hoverEnd(e);\n        }\n      };\n\n      /**\n       * Move within element\n       */\n      var moveListener = function moveListener(e) {\n        if (getPointerType(e) !== 'touch') {\n          if (onHoverUpdate != null) {\n            // Not all browsers have these properties\n            if (e.x == null) {\n              e.x = e.clientX;\n            }\n            if (e.y == null) {\n              e.y = e.clientY;\n            }\n            onHoverUpdate(e);\n          }\n        }\n      };\n\n      /**\n       * Start the hover gesture\n       */\n      var hoverStart = function hoverStart(e) {\n        if (onHoverStart != null) {\n          onHoverStart(e);\n        }\n        if (onHoverChange != null) {\n          onHoverChange(true);\n        }\n        // Set the listeners needed for the rest of the hover gesture.\n        if (onHoverUpdate != null) {\n          addMoveListener(target, !disabled ? moveListener : null);\n        }\n        addLeaveListener(target, !disabled ? leaveListener : null);\n      };\n\n      /**\n       * Enter element\n       */\n      var enterListener = function enterListener(e) {\n        var target = targetRef.current;\n        if (target != null && getPointerType(e) !== 'touch') {\n          if (contain) {\n            dispatchCustomEvent(target, lockEventType);\n          }\n          hoverStart(e);\n          var lockListener = function lockListener(lockEvent) {\n            if (lockEvent.target !== target) {\n              hoverEnd(e);\n            }\n          };\n          var unlockListener = function unlockListener(lockEvent) {\n            if (lockEvent.target !== target) {\n              hoverStart(e);\n            }\n          };\n          addLockListener(target, !disabled ? lockListener : null);\n          addUnlockListener(target, !disabled ? unlockListener : null);\n        }\n      };\n      addEnterListener(target, !disabled ? enterListener : null);\n    }\n  }, [addEnterListener, addMoveListener, addLeaveListener, addLockListener, addUnlockListener, contain, disabled, onHoverStart, onHoverChange, onHoverUpdate, onHoverEnd, targetRef]);\n}"], "mappings": "AASA,SAASA,WAAW;AACpB,OAAOC,QAAQ;AACf,OAAOC,eAAe;AAUtB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,IAAI,GAAG;EACTC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,aAAa,GAAG,sBAAsB;AAC1C,IAAIC,eAAe,GAAG,wBAAwB;AAC9C,IAAIC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OAAS,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,YAAY,IAAI,IAAI,CAAC;AAAA;AACjG,SAASC,mBAAmBA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAClD,IAAIC,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,aAAa,CAAC;EAC/C,IAAIC,IAAI,GAAGJ,OAAO,IAAIX,WAAW;IAC/BgB,YAAY,GAAGD,IAAI,CAACE,OAAO;IAC3BA,OAAO,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,YAAY;IACvDE,eAAe,GAAGH,IAAI,CAACI,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,eAAe;IAChEE,MAAM,GAAGL,IAAI,CAACK,MAAM;EACtBR,KAAK,CAACS,eAAe,CAACX,IAAI,EAAEO,OAAO,EAAEE,UAAU,EAAEC,MAAM,CAAC;EACxDX,MAAM,CAACa,aAAa,CAACV,KAAK,CAAC;AAC7B;AAGA,SAASW,cAAcA,CAACX,KAAK,EAAE;EAC7B,IAAIY,WAAW,GAAGZ,KAAK,CAACY,WAAW;EACnC,OAAOA,WAAW,IAAI,IAAI,GAAGA,WAAW,GAAG3B,WAAW,CAAC,CAAC;AAC1D;AACA,eAAe,SAAS4B,QAAQA,CAACC,SAAS,EAAEC,MAAM,EAAE;EAClD,IAAIC,OAAO,GAAGD,MAAM,CAACC,OAAO;IAC1BC,QAAQ,GAAGF,MAAM,CAACE,QAAQ;IAC1BC,YAAY,GAAGH,MAAM,CAACG,YAAY;IAClCC,aAAa,GAAGJ,MAAM,CAACI,aAAa;IACpCC,aAAa,GAAGL,MAAM,CAACK,aAAa;IACpCC,UAAU,GAAGN,MAAM,CAACM,UAAU;EAChC,IAAIC,QAAQ,GAAG7B,oBAAoB,CAAC,CAAC;EACrC,IAAI8B,eAAe,GAAGrC,QAAQ,CAACoC,QAAQ,GAAG,aAAa,GAAG,WAAW,EAAEjC,IAAI,CAAC;EAC5E,IAAImC,gBAAgB,GAAGtC,QAAQ,CAACoC,QAAQ,GAAG,cAAc,GAAG,YAAY,EAAEjC,IAAI,CAAC;EAC/E,IAAIoC,gBAAgB,GAAGvC,QAAQ,CAACoC,QAAQ,GAAG,cAAc,GAAG,YAAY,EAAEjC,IAAI,CAAC;EAE/E,IAAIqC,eAAe,GAAGxC,QAAQ,CAACK,aAAa,EAAEF,IAAI,CAAC;EACnD,IAAIsC,iBAAiB,GAAGzC,QAAQ,CAACM,eAAe,EAAEH,IAAI,CAAC;EACvDF,eAAe,CAAC,YAAM;IACpB,IAAIU,MAAM,GAAGiB,SAAS,CAACc,OAAO;IAC9B,IAAI/B,MAAM,KAAK,IAAI,EAAE;MAInB,IAAIgC,QAAQ,GAAG,SAASA,QAAQA,CAACC,CAAC,EAAE;QAClC,IAAIT,UAAU,IAAI,IAAI,EAAE;UACtBA,UAAU,CAACS,CAAC,CAAC;QACf;QACA,IAAIX,aAAa,IAAI,IAAI,EAAE;UACzBA,aAAa,CAAC,KAAK,CAAC;QACtB;QAEAI,eAAe,CAAC1B,MAAM,EAAE,IAAI,CAAC;QAC7B4B,gBAAgB,CAAC5B,MAAM,EAAE,IAAI,CAAC;MAChC,CAAC;MAKD,IAAIkC,aAAa,GAAG,SAASA,aAAaA,CAACD,CAAC,EAAE;QAC5C,IAAIjC,MAAM,GAAGiB,SAAS,CAACc,OAAO;QAC9B,IAAI/B,MAAM,IAAI,IAAI,IAAIc,cAAc,CAACmB,CAAC,CAAC,KAAK,OAAO,EAAE;UACnD,IAAId,OAAO,EAAE;YACXpB,mBAAmB,CAACC,MAAM,EAAEL,eAAe,CAAC;UAC9C;UACAqC,QAAQ,CAACC,CAAC,CAAC;QACb;MACF,CAAC;MAKD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACF,CAAC,EAAE;QAC1C,IAAInB,cAAc,CAACmB,CAAC,CAAC,KAAK,OAAO,EAAE;UACjC,IAAIV,aAAa,IAAI,IAAI,EAAE;YAEzB,IAAIU,CAAC,CAACG,CAAC,IAAI,IAAI,EAAE;cACfH,CAAC,CAACG,CAAC,GAAGH,CAAC,CAACI,OAAO;YACjB;YACA,IAAIJ,CAAC,CAACK,CAAC,IAAI,IAAI,EAAE;cACfL,CAAC,CAACK,CAAC,GAAGL,CAAC,CAACM,OAAO;YACjB;YACAhB,aAAa,CAACU,CAAC,CAAC;UAClB;QACF;MACF,CAAC;MAKD,IAAIO,UAAU,GAAG,SAASA,UAAUA,CAACP,CAAC,EAAE;QACtC,IAAIZ,YAAY,IAAI,IAAI,EAAE;UACxBA,YAAY,CAACY,CAAC,CAAC;QACjB;QACA,IAAIX,aAAa,IAAI,IAAI,EAAE;UACzBA,aAAa,CAAC,IAAI,CAAC;QACrB;QAEA,IAAIC,aAAa,IAAI,IAAI,EAAE;UACzBG,eAAe,CAAC1B,MAAM,EAAE,CAACoB,QAAQ,GAAGe,YAAY,GAAG,IAAI,CAAC;QAC1D;QACAP,gBAAgB,CAAC5B,MAAM,EAAE,CAACoB,QAAQ,GAAGc,aAAa,GAAG,IAAI,CAAC;MAC5D,CAAC;MAKD,IAAIO,aAAa,GAAG,SAASA,aAAaA,CAACR,CAAC,EAAE;QAC5C,IAAIjC,MAAM,GAAGiB,SAAS,CAACc,OAAO;QAC9B,IAAI/B,MAAM,IAAI,IAAI,IAAIc,cAAc,CAACmB,CAAC,CAAC,KAAK,OAAO,EAAE;UACnD,IAAId,OAAO,EAAE;YACXpB,mBAAmB,CAACC,MAAM,EAAEN,aAAa,CAAC;UAC5C;UACA8C,UAAU,CAACP,CAAC,CAAC;UACb,IAAIS,YAAY,GAAG,SAASA,YAAYA,CAACC,SAAS,EAAE;YAClD,IAAIA,SAAS,CAAC3C,MAAM,KAAKA,MAAM,EAAE;cAC/BgC,QAAQ,CAACC,CAAC,CAAC;YACb;UACF,CAAC;UACD,IAAIW,cAAc,GAAG,SAASA,cAAcA,CAACD,SAAS,EAAE;YACtD,IAAIA,SAAS,CAAC3C,MAAM,KAAKA,MAAM,EAAE;cAC/BwC,UAAU,CAACP,CAAC,CAAC;YACf;UACF,CAAC;UACDJ,eAAe,CAAC7B,MAAM,EAAE,CAACoB,QAAQ,GAAGsB,YAAY,GAAG,IAAI,CAAC;UACxDZ,iBAAiB,CAAC9B,MAAM,EAAE,CAACoB,QAAQ,GAAGwB,cAAc,GAAG,IAAI,CAAC;QAC9D;MACF,CAAC;MACDjB,gBAAgB,CAAC3B,MAAM,EAAE,CAACoB,QAAQ,GAAGqB,aAAa,GAAG,IAAI,CAAC;IAC5D;EACF,CAAC,EAAE,CAACd,gBAAgB,EAAED,eAAe,EAAEE,gBAAgB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEX,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,aAAa,EAAEC,UAAU,EAAEP,SAAS,CAAC,CAAC;AACrL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}