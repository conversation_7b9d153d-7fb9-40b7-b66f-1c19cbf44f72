{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport * as React from 'react';\nexport default function useEventEmitter(listen) {\n  var listenRef = React.useRef(listen);\n  React.useEffect(function () {\n    listenRef.current = listen;\n  });\n  var listeners = React.useRef(Object.create(null));\n  var create = React.useCallback(function (target) {\n    var removeListener = function removeListener(type, callback) {\n      var callbacks = listeners.current[type] ? listeners.current[type][target] : undefined;\n      if (!callbacks) {\n        return;\n      }\n      var index = callbacks.indexOf(callback);\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    };\n    var addListener = function addListener(type, callback) {\n      listeners.current[type] = listeners.current[type] || {};\n      listeners.current[type][target] = listeners.current[type][target] || [];\n      listeners.current[type][target].push(callback);\n      var removed = false;\n      return function () {\n        if (!removed) {\n          removed = true;\n          removeListener(type, callback);\n        }\n      };\n    };\n    return {\n      addListener: addListener,\n      removeListener: removeListener\n    };\n  }, []);\n  var emit = React.useCallback(function (_ref) {\n    var _ref2;\n    var _items$target, _listenRef$current;\n    var type = _ref.type,\n      data = _ref.data,\n      target = _ref.target,\n      canPreventDefault = _ref.canPreventDefault;\n    var items = listeners.current[type] || {};\n    var callbacks = target !== undefined ? (_items$target = items[target]) === null || _items$target === void 0 ? void 0 : _items$target.slice() : (_ref2 = []).concat.apply(_ref2, _toConsumableArray(Object.keys(items).map(function (t) {\n      return items[t];\n    }))).filter(function (cb, i, self) {\n      return self.lastIndexOf(cb) === i;\n    });\n    var event = {\n      get type() {\n        return type;\n      }\n    };\n    if (target !== undefined) {\n      Object.defineProperty(event, 'target', {\n        enumerable: true,\n        get: function get() {\n          return target;\n        }\n      });\n    }\n    if (data !== undefined) {\n      Object.defineProperty(event, 'data', {\n        enumerable: true,\n        get: function get() {\n          return data;\n        }\n      });\n    }\n    if (canPreventDefault) {\n      var defaultPrevented = false;\n      Object.defineProperties(event, {\n        defaultPrevented: {\n          enumerable: true,\n          get: function get() {\n            return defaultPrevented;\n          }\n        },\n        preventDefault: {\n          enumerable: true,\n          value: function value() {\n            defaultPrevented = true;\n          }\n        }\n      });\n    }\n    (_listenRef$current = listenRef.current) === null || _listenRef$current === void 0 ? void 0 : _listenRef$current.call(listenRef, event);\n    callbacks === null || callbacks === void 0 ? void 0 : callbacks.forEach(function (cb) {\n      return cb(event);\n    });\n    return event;\n  }, []);\n  return React.useMemo(function () {\n    return {\n      create: create,\n      emit: emit\n    };\n  }, [create, emit]);\n}", "map": {"version": 3, "names": ["React", "useEventEmitter", "listen", "listenRef", "useRef", "useEffect", "current", "listeners", "Object", "create", "useCallback", "target", "removeListener", "type", "callback", "callbacks", "undefined", "index", "indexOf", "splice", "addListener", "push", "removed", "emit", "_ref", "_ref2", "_items$target", "_listenRef$current", "data", "canPreventDefault", "items", "slice", "concat", "apply", "_toConsumableArray", "keys", "map", "t", "filter", "cb", "i", "self", "lastIndexOf", "event", "defineProperty", "enumerable", "get", "defaultPrevented", "defineProperties", "preventDefault", "value", "call", "for<PERSON>ach", "useMemo"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\useEventEmitter.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport type { EventArg, EventConsumer, EventEmitter } from './types';\n\nexport type NavigationEventEmitter<T extends Record<string, any>> =\n  EventEmitter<T> & {\n    create: (target: string) => EventConsumer<T>;\n  };\n\ntype Listeners = ((e: any) => void)[];\n\n/**\n * Hook to manage the event system used by the navigator to notify screens of various events.\n */\nexport default function useEventEmitter<T extends Record<string, any>>(\n  listen?: (e: any) => void\n): NavigationEventEmitter<T> {\n  const listenRef = React.useRef(listen);\n\n  React.useEffect(() => {\n    listenRef.current = listen;\n  });\n\n  const listeners = React.useRef<Record<string, Record<string, Listeners>>>(\n    Object.create(null)\n  );\n\n  const create = React.useCallback((target: string) => {\n    const removeListener = (type: string, callback: (data: any) => void) => {\n      const callbacks = listeners.current[type]\n        ? listeners.current[type][target]\n        : undefined;\n\n      if (!callbacks) {\n        return;\n      }\n\n      const index = callbacks.indexOf(callback);\n\n      if (index > -1) {\n        callbacks.splice(index, 1);\n      }\n    };\n\n    const addListener = (type: string, callback: (data: any) => void) => {\n      listeners.current[type] = listeners.current[type] || {};\n      listeners.current[type][target] = listeners.current[type][target] || [];\n      listeners.current[type][target].push(callback);\n\n      let removed = false;\n      return () => {\n        // Prevent removing other listeners when unsubscribing same listener multiple times\n        if (!removed) {\n          removed = true;\n          removeListener(type, callback);\n        }\n      };\n    };\n\n    return {\n      addListener,\n      removeListener,\n    };\n  }, []);\n\n  const emit = React.useCallback(\n    ({\n      type,\n      data,\n      target,\n      canPreventDefault,\n    }: {\n      type: string;\n      data?: any;\n      target?: string;\n      canPreventDefault?: boolean;\n    }) => {\n      const items = listeners.current[type] || {};\n\n      // Copy the current list of callbacks in case they are mutated during execution\n      const callbacks =\n        target !== undefined\n          ? items[target]?.slice()\n          : ([] as Listeners)\n              .concat(...Object.keys(items).map((t) => items[t]))\n              .filter((cb, i, self) => self.lastIndexOf(cb) === i);\n\n      const event: EventArg<any, any, any> = {\n        get type() {\n          return type;\n        },\n      };\n\n      if (target !== undefined) {\n        Object.defineProperty(event, 'target', {\n          enumerable: true,\n          get() {\n            return target;\n          },\n        });\n      }\n\n      if (data !== undefined) {\n        Object.defineProperty(event, 'data', {\n          enumerable: true,\n          get() {\n            return data;\n          },\n        });\n      }\n\n      if (canPreventDefault) {\n        let defaultPrevented = false;\n\n        Object.defineProperties(event, {\n          defaultPrevented: {\n            enumerable: true,\n            get() {\n              return defaultPrevented;\n            },\n          },\n          preventDefault: {\n            enumerable: true,\n            value() {\n              defaultPrevented = true;\n            },\n          },\n        });\n      }\n\n      listenRef.current?.(event);\n\n      callbacks?.forEach((cb) => cb(event));\n\n      return event as any;\n    },\n    []\n  );\n\n  return React.useMemo(() => ({ create, emit }), [create, emit]);\n}\n"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAc9B,eAAe,SAASC,eAAeA,CACrCC,MAAyB,EACE;EAC3B,IAAMC,SAAS,GAAGH,KAAK,CAACI,MAAM,CAACF,MAAM,CAAC;EAEtCF,KAAK,CAACK,SAAS,CAAC,YAAM;IACpBF,SAAS,CAACG,OAAO,GAAGJ,MAAM;EAC5B,CAAC,CAAC;EAEF,IAAMK,SAAS,GAAGP,KAAK,CAACI,MAAM,CAC5BI,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC,CACpB;EAED,IAAMA,MAAM,GAAGT,KAAK,CAACU,WAAW,CAAE,UAAAC,MAAc,EAAK;IACnD,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAY,EAAEC,QAA6B,EAAK;MACtE,IAAMC,SAAS,GAAGR,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,GACrCN,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,GAC/BK,SAAS;MAEb,IAAI,CAACD,SAAS,EAAE;QACd;MACF;MAEA,IAAME,KAAK,GAAGF,SAAS,CAACG,OAAO,CAACJ,QAAQ,CAAC;MAEzC,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;QACdF,SAAS,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5B;IACF,CAAC;IAED,IAAMG,WAAW,GAAG,SAAdA,WAAWA,CAAIP,IAAY,EAAEC,QAA6B,EAAK;MACnEP,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,GAAGN,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;MACvDN,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,GAAGJ,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,IAAI,EAAE;MACvEJ,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,CAACF,MAAM,CAAC,CAACU,IAAI,CAACP,QAAQ,CAAC;MAE9C,IAAIQ,OAAO,GAAG,KAAK;MACnB,OAAO,YAAM;QAEX,IAAI,CAACA,OAAO,EAAE;UACZA,OAAO,GAAG,IAAI;UACdV,cAAc,CAACC,IAAI,EAAEC,QAAQ,CAAC;QAChC;MACF,CAAC;IACH,CAAC;IAED,OAAO;MACLM,WAAW,EAAXA,WAAW;MACXR,cAAA,EAAAA;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMW,IAAI,GAAGvB,KAAK,CAACU,WAAW,CAC5B,UAAAc,IAAA,EAUM;IAAA,IAAAC,KAAA;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IAAA,IATJd,IAAI,GASLW,IAAA,CATCX,IAAI;MACJe,IAAI,GAQLJ,IAAA,CARCI,IAAI;MACJjB,MAAM,GAOPa,IAAA,CAPCb,MAAM;MACNkB,iBAAA,GAMDL,IAAA,CANCK,iBAAA;IAOA,IAAMC,KAAK,GAAGvB,SAAS,CAACD,OAAO,CAACO,IAAI,CAAC,IAAI,CAAC,CAAC;IAG3C,IAAME,SAAS,GACbJ,MAAM,KAAKK,SAAS,IAAAU,aAAA,GAChBI,KAAK,CAACnB,MAAM,CAAC,cAAAe,aAAA,uBAAbA,aAAA,CAAeK,KAAK,EAAE,GACrB,CAAAN,KAAA,KAAE,EACAO,MAAM,CAAAC,KAAA,CAAAR,KAAA,EAAAS,kBAAA,CAAI1B,MAAM,CAAC2B,IAAI,CAACL,KAAK,CAAC,CAACM,GAAG,CAAE,UAAAC,CAAC;MAAA,OAAKP,KAAK,CAACO,CAAC,CAAC;IAAA,EAAC,EAAC,CAClDC,MAAM,CAAC,UAACC,EAAE,EAAEC,CAAC,EAAEC,IAAI;MAAA,OAAKA,IAAI,CAACC,WAAW,CAACH,EAAE,CAAC,KAAKC,CAAC;IAAA,EAAC;IAE5D,IAAMG,KAA8B,GAAG;MACrC,IAAI9B,IAAIA,CAAA,EAAG;QACT,OAAOA,IAAI;MACb;IACF,CAAC;IAED,IAAIF,MAAM,KAAKK,SAAS,EAAE;MACxBR,MAAM,CAACoC,cAAc,CAACD,KAAK,EAAE,QAAQ,EAAE;QACrCE,UAAU,EAAE,IAAI;QAChBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJ,OAAOnC,MAAM;QACf;MACF,CAAC,CAAC;IACJ;IAEA,IAAIiB,IAAI,KAAKZ,SAAS,EAAE;MACtBR,MAAM,CAACoC,cAAc,CAACD,KAAK,EAAE,MAAM,EAAE;QACnCE,UAAU,EAAE,IAAI;QAChBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;UACJ,OAAOlB,IAAI;QACb;MACF,CAAC,CAAC;IACJ;IAEA,IAAIC,iBAAiB,EAAE;MACrB,IAAIkB,gBAAgB,GAAG,KAAK;MAE5BvC,MAAM,CAACwC,gBAAgB,CAACL,KAAK,EAAE;QAC7BI,gBAAgB,EAAE;UAChBF,UAAU,EAAE,IAAI;UAChBC,GAAG,WAAHA,GAAGA,CAAA,EAAG;YACJ,OAAOC,gBAAgB;UACzB;QACF,CAAC;QACDE,cAAc,EAAE;UACdJ,UAAU,EAAE,IAAI;UAChBK,KAAK,WAALA,KAAKA,CAAA,EAAG;YACNH,gBAAgB,GAAG,IAAI;UACzB;QACF;MACF,CAAC,CAAC;IACJ;IAEA,CAAApB,kBAAA,GAAAxB,SAAS,CAACG,OAAO,cAAAqB,kBAAA,uBAAjBA,kBAAA,CAAAwB,IAAA,CAAAhD,SAAS,EAAWwC,KAAK,CAAC;IAE1B5B,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEqC,OAAO,CAAE,UAAAb,EAAE;MAAA,OAAKA,EAAE,CAACI,KAAK,CAAC;IAAA,EAAC;IAErC,OAAOA,KAAK;EACd,CAAC,EACD,EAAE,CACH;EAED,OAAO3C,KAAK,CAACqD,OAAO,CAAC;IAAA,OAAO;MAAE5C,MAAM,EAANA,MAAM;MAAEc,IAAA,EAAAA;IAAK,CAAC;EAAA,CAAC,EAAE,CAACd,MAAM,EAAEc,IAAI,CAAC,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}