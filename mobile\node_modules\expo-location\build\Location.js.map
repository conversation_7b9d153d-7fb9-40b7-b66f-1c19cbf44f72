{"version": 3, "file": "Location.js", "sourceRoot": "", "sources": ["../src/Location.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,gBAAgB,EAGhB,oBAAoB,EACpB,QAAQ,GACT,MAAM,mBAAmB,CAAC;AAE3B,OAAO,YAAY,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EACL,gBAAgB,EAchB,oBAAoB,EACpB,2BAA2B,EAC3B,6BAA6B,GAE9B,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAElG,cAAc;AACd;;;;GAIG;AACH,SAAS,eAAe,CAAC,OAAe,IAAG,CAAC;AAE5C,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,sBAAsB;IAC1C,OAAO,YAAY,CAAC,sBAAsB,EAAE,CAAC;AAC/C,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,0BAA0B;IAC9C,kFAAkF;IAClF,qGAAqG;IACrG,+GAA+G;IAC/G,wFAAwF;IAExF,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;QAC7B,OAAO,YAAY,CAAC,0BAA0B,EAAE,CAAC;KAClD;AACH,CAAC;AAED,cAAc;AACd;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC3C,UAA2B,EAAE;IAE7B,OAAO,YAAY,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AACvD,CAAC;AAED,cAAc;AACd;;;;;;;;;GASG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAC7C,UAAoC,EAAE;IAEtC,OAAO,YAAY,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,OAAwB,EACxB,QAA0B;IAE1B,MAAM,OAAO,GAAG,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC9D,MAAM,YAAY,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IAE5D,OAAO;QACL,MAAM;YACJ,kBAAkB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;KACF,CAAC;AACJ,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QACnC,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,MAAM,YAAY,GAAG,MAAM,iBAAiB,CAAC,CAAC,OAAO,EAAE,EAAE;YACvD,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBACrC,YAAY,CAAC,MAAM,EAAE,CAAC;gBACtB,OAAO,CAAC,OAAO,CAAC,CAAC;aAClB;iBAAM;gBACL,KAAK,IAAI,CAAC,CAAC;aACZ;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,QAAiC;IAEjC,MAAM,OAAO,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IAC7D,MAAM,YAAY,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAE/C,OAAO;QACL,MAAM;YACJ,iBAAiB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;KACF,CAAC;AACJ,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,KAAK,UAAU,YAAY,CAChC,OAAe,EACf,OAAkC;IAElC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,MAAM,IAAI,SAAS,CAAC,4CAA4C,OAAO,WAAW,CAAC,CAAC;KACrF;IAED,IAAI,OAAO,EAAE,aAAa,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACnD,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,IAAI,CACV,sFAAsF;gBACpF,oFAAoF,CACvF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;KACX;IAED,OAAO,MAAM,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CACvC,QAAkE,EAClE,OAAkC;IAElC,IAAI,OAAO,QAAQ,CAAC,QAAQ,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,SAAS,KAAK,QAAQ,EAAE;QACnF,MAAM,IAAI,SAAS,CACjB,kGAAkG,CACnG,CAAC;KACH;IAED,IAAI,OAAO,EAAE,aAAa,IAAI,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE;QACnD,IAAI,OAAO,EAAE;YACX,OAAO,CAAC,IAAI,CACV,sFAAsF;gBACpF,oFAAoF,CACvF,CAAC;SACH;QACD,OAAO,EAAE,CAAC;KACX;IAED,OAAO,MAAM,YAAY,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AAC1D,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB;IACvC,OAAO,CAAC,IAAI,CACV,uIAAuI,CACxI,CAAC;IACF,OAAO,MAAM,YAAY,CAAC,mBAAmB,EAAE,CAAC;AAClD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,OAAO,CAAC,IAAI,CACV,mJAAmJ,CACpJ,CAAC;IAEF,OAAO,MAAM,YAAY,CAAC,uBAAuB,EAAE,CAAC;AACtD,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,6BAA6B;IACjD,OAAO,MAAM,YAAY,CAAC,6BAA6B,EAAE,CAAC;AAC5D,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,iCAAiC;IACrD,OAAO,MAAM,YAAY,CAAC,iCAAiC,EAAE,CAAC;AAChE,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,oBAAoB,CAAC;IAC3D,SAAS,EAAE,6BAA6B;IACxC,aAAa,EAAE,iCAAiC;CACjD,CAAC,CAAC;AAEH,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,6BAA6B;IACjD,OAAO,MAAM,YAAY,CAAC,6BAA6B,EAAE,CAAC;AAC5D,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,iCAAiC;IACrD,OAAO,MAAM,YAAY,CAAC,iCAAiC,EAAE,CAAC;AAChE,CAAC;AAED,cAAc;AACd;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,oBAAoB,CAAC;IAC3D,SAAS,EAAE,6BAA6B;IACxC,aAAa,EAAE,iCAAiC;CACjD,CAAC,CAAC;AAEH,uBAAuB;AAEvB,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAC3C,OAAO,MAAM,YAAY,CAAC,uBAAuB,EAAE,CAAC;AACtD,CAAC;AAED,kCAAkC;AAElC,SAAS,iBAAiB,CAAC,QAAgB;IACzC,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;QAC7C,MAAM,IAAI,KAAK,CAAC,gDAAgD,QAAQ,WAAW,CAAC,CAAC;KACtF;AACH,CAAC;AAED,eAAe;AACf,MAAM,CAAC,KAAK,UAAU,kCAAkC;IACtD,MAAM,cAAc,GAAG,MAAM,sBAAsB,EAAE,CAAC;IACtD,OAAO,cAAc,CAAC,qBAAqB,CAAC;AAC9C,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAC7C,QAAgB,EAChB,UAA+B,EAAE,QAAQ,EAAE,gBAAgB,CAAC,QAAQ,EAAE;IAEtE,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5B,MAAM,YAAY,CAAC,yBAAyB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAClE,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAAC,QAAgB;IAC7D,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5B,MAAM,YAAY,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;AACxD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,8BAA8B,CAAC,QAAgB;IACnE,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5B,OAAO,YAAY,CAAC,8BAA8B,CAAC,QAAQ,CAAC,CAAC;AAC/D,CAAC;AAED,iBAAiB;AAEjB,SAAS,gBAAgB,CAAC,OAAyB;IACjD,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACpC,MAAM,IAAI,KAAK,CACb,qGAAqG,CACtG,CAAC;KACH;IACD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACvC,MAAM,IAAI,SAAS,CAAC,4CAA4C,MAAM,CAAC,QAAQ,YAAY,CAAC,CAAC;SAC9F;QACD,IAAI,OAAO,MAAM,CAAC,SAAS,KAAK,QAAQ,EAAE;YACxC,MAAM,IAAI,SAAS,CACjB,6CAA6C,MAAM,CAAC,SAAS,YAAY,CAC1E,CAAC;SACH;QACD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;YACrC,MAAM,IAAI,SAAS,CAAC,0CAA0C,MAAM,CAAC,MAAM,YAAY,CAAC,CAAC;SAC1F;KACF;AACH,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,QAAgB,EAChB,UAA4B,EAAE;IAE9B,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5B,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1B,MAAM,YAAY,CAAC,oBAAoB,CAAC,QAAQ,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;AACjE,CAAC;AAED,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,QAAgB;IACxD,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5B,MAAM,YAAY,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACnD,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB,CAAC,QAAgB;IAC9D,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5B,OAAO,YAAY,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AAC1D,CAAC;AAED,OAAO,EAAE,oBAAoB,IAAI,YAAY,EAAE,kBAAkB,EAAE,CAAC;AAEpE,OAAO,EACL,gBAAgB,IAAI,QAAQ,EAC5B,oBAAoB,IAAI,YAAY,EACpC,2BAA2B,IAAI,mBAAmB,EAClD,6BAA6B,IAAI,qBAAqB,EACtD,gBAAgB,EAEhB,eAAe,GAChB,CAAC;AAEF,OAAO,EAAE,6BAA6B,EAAE,MAAM,uBAAuB,CAAC;AACtE,cAAc,kBAAkB,CAAC", "sourcesContent": ["import {\n  PermissionStatus,\n  PermissionResponse,\n  PermissionHookOptions,\n  createPer<PERSON>Hook,\n  Platform,\n} from 'expo-modules-core';\n\nimport ExpoLocation from './ExpoLocation';\nimport {\n  LocationAccuracy,\n  LocationCallback,\n  LocationGeocodedAddress,\n  LocationGeocodedLocation,\n  LocationHeadingCallback,\n  LocationHeadingObject,\n  LocationLastKnownOptions,\n  LocationObject,\n  LocationOptions,\n  LocationPermissionResponse,\n  LocationProviderStatus,\n  LocationRegion,\n  LocationSubscription,\n  LocationTaskOptions,\n  LocationActivityType,\n  LocationGeofencingEventType,\n  LocationGeofencingRegionState,\n  LocationGeocodingOptions,\n} from './Location.types';\nimport { LocationEventEmitter } from './LocationEventEmitter';\nimport { LocationSubscriber, HeadingSubscriber, _getCurrentWatchId } from './LocationSubscribers';\n\n// @needsAudit\n/**\n * @deprecated The Geocoding web api is no longer available from SDK 49 onwards. Use [Place Autocomplete](https://developers.google.com/maps/documentation/places/web-service/autocomplete) instead.\n * @param apiKey Google API key obtained from Google API Console. This API key must have `Geocoding API`\n * enabled, otherwise your geocoding requests will be denied.\n */\nfunction setGoogleApiKey(_apiKey: string) {}\n\n// @needsAudit\n/**\n * Check status of location providers.\n * @return A promise which fulfills with an object of type [LocationProviderStatus](#locationproviderstatus).\n */\nexport async function getProviderStatusAsync(): Promise<LocationProviderStatus> {\n  return ExpoLocation.getProviderStatusAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to turn on high accuracy location mode which enables network provider that uses\n * Google Play services to improve location accuracy and location-based services.\n * @return A promise resolving as soon as the user accepts the dialog. Rejects if denied.\n */\nexport async function enableNetworkProviderAsync(): Promise<void> {\n  // If network provider is disabled (user's location mode is set to \"Device only\"),\n  // Android's location provider may not give you any results. Use this method in order to ask the user\n  // to change the location mode to \"High accuracy\" which uses Google Play services and enables network provider.\n  // `getCurrentPositionAsync` and `watchPositionAsync` are doing it automatically anyway.\n\n  if (Platform.OS === 'android') {\n    return ExpoLocation.enableNetworkProviderAsync();\n  }\n}\n\n// @needsAudit\n/**\n * Requests for one-time delivery of the user's current location.\n * Depending on given `accuracy` option it may take some time to resolve,\n * especially when you're inside a building.\n * > __Note:__ Calling it causes the location manager to obtain a location fix which may take several\n * > seconds. Consider using [`Location.getLastKnownPositionAsync`](#locationgetlastknownpositionasyncoptions)\n * > if you expect to get a quick response and high accuracy is not required.\n * @param options\n * @return A promise which fulfills with an object of type [`LocationObject`](#locationobject).\n */\nexport async function getCurrentPositionAsync(\n  options: LocationOptions = {}\n): Promise<LocationObject> {\n  return ExpoLocation.getCurrentPositionAsync(options);\n}\n\n// @needsAudit\n/**\n * Gets the last known position of the device or `null` if it's not available or doesn't match given\n * requirements such as maximum age or required accuracy.\n * It's considered to be faster than `getCurrentPositionAsync` as it doesn't request for the current\n * location, but keep in mind the returned location may not be up-to-date.\n * @param options\n * @return A promise which fulfills with an object of type [LocationObject](#locationobject) or\n * `null` if it's not available or doesn't match given requirements such as maximum age or required\n * accuracy.\n */\nexport async function getLastKnownPositionAsync(\n  options: LocationLastKnownOptions = {}\n): Promise<LocationObject | null> {\n  return ExpoLocation.getLastKnownPositionAsync(options);\n}\n\n// @needsAudit\n/**\n * Subscribe to location updates from the device. Please note that updates will only occur while the\n * application is in the foreground. To get location updates while in background you'll need to use\n * [Location.startLocationUpdatesAsync](#locationstartlocationupdatesasynctaskname-options).\n * @param options\n * @param callback This function is called on each location update. It receives an object of type\n * [`LocationObject`](#locationobject) as the first argument.\n * @return A promise which fulfills with a [`LocationSubscription`](#locationsubscription) object.\n */\nexport async function watchPositionAsync(\n  options: LocationOptions,\n  callback: LocationCallback\n): Promise<LocationSubscription> {\n  const watchId = LocationSubscriber.registerCallback(callback);\n  await ExpoLocation.watchPositionImplAsync(watchId, options);\n\n  return {\n    remove() {\n      LocationSubscriber.unregisterCallback(watchId);\n    },\n  };\n}\n\n// @needsAudit\n/**\n * Gets the current heading information from the device. To simplify, it calls `watchHeadingAsync`\n * and waits for a couple of updates, and then returns the one that is accurate enough.\n * @return A promise which fulfills with an object of type [LocationHeadingObject](#locationheadingobject).\n */\nexport async function getHeadingAsync(): Promise<LocationHeadingObject> {\n  return new Promise(async (resolve) => {\n    let tries = 0;\n\n    const subscription = await watchHeadingAsync((heading) => {\n      if (heading.accuracy > 1 || tries > 5) {\n        subscription.remove();\n        resolve(heading);\n      } else {\n        tries += 1;\n      }\n    });\n  });\n}\n\n// @needsAudit\n/**\n * Subscribe to compass updates from the device.\n * @param callback This function is called on each compass update. It receives an object of type\n * [LocationHeadingObject](#locationheadingobject) as the first argument.\n * @return A promise which fulfills with a [`LocationSubscription`](#locationsubscription) object.\n */\nexport async function watchHeadingAsync(\n  callback: LocationHeadingCallback\n): Promise<LocationSubscription> {\n  const watchId = HeadingSubscriber.registerCallback(callback);\n  await ExpoLocation.watchDeviceHeading(watchId);\n\n  return {\n    remove() {\n      HeadingSubscriber.unregisterCallback(watchId);\n    },\n  };\n}\n\n// @needsAudit\n/**\n * Geocode an address string to latitude-longitude location.\n * > **Note**: Using the Geocoding web api is no longer supported. Use [Place Autocomplete](https://developers.google.com/maps/documentation/places/web-service/autocomplete) instead.\n *\n * > **Note**: Geocoding is resource consuming and has to be used reasonably. Creating too many\n * > requests at a time can result in an error, so they have to be managed properly.\n * > It's also discouraged to use geocoding while the app is in the background and its results won't\n * > be shown to the user immediately.\n *\n * > On Android, you must request a location permission (`Permissions.LOCATION`) from the user\n * > before geocoding can be used.\n * @param address A string representing address, eg. `\"Baker Street London\"`.\n * @param options\n * @return A promise which fulfills with an array (in most cases its size is 1) of [`LocationGeocodedLocation`](#locationgeocodedlocation) objects.\n */\nexport async function geocodeAsync(\n  address: string,\n  options?: LocationGeocodingOptions\n): Promise<LocationGeocodedLocation[]> {\n  if (typeof address !== 'string') {\n    throw new TypeError(`Address to geocode must be a string. Got ${address} instead.`);\n  }\n\n  if (options?.useGoogleMaps || Platform.OS === 'web') {\n    if (__DEV__) {\n      console.warn(\n        'The Geocoding API has been removed in SDK 49, use Place Autocomplete service instead' +\n          '(https://developers.google.com/maps/documentation/places/web-service/autocomplete)'\n      );\n    }\n    return [];\n  }\n\n  return await ExpoLocation.geocodeAsync(address);\n}\n\n// @needsAudit\n/**\n * Reverse geocode a location to postal address.\n * > **Note**: Using the Geocoding web api is no longer supported. Use [Place Autocomplete](https://developers.google.com/maps/documentation/places/web-service/autocomplete) instead.\n *\n * > **Note**: Geocoding is resource consuming and has to be used reasonably. Creating too many\n * > requests at a time can result in an error, so they have to be managed properly.\n * > It's also discouraged to use geocoding while the app is in the background and its results won't\n * > be shown to the user immediately.\n *\n * > On Android, you must request a location permission (`Permissions.LOCATION`) from the user\n * > before geocoding can be used.\n * @param location An object representing a location.\n * @param options\n * @return A promise which fulfills with an array (in most cases its size is 1) of [`LocationGeocodedAddress`](#locationgeocodedaddress) objects.\n */\nexport async function reverseGeocodeAsync(\n  location: Pick<LocationGeocodedLocation, 'latitude' | 'longitude'>,\n  options?: LocationGeocodingOptions\n): Promise<LocationGeocodedAddress[]> {\n  if (typeof location.latitude !== 'number' || typeof location.longitude !== 'number') {\n    throw new TypeError(\n      'Location to reverse-geocode must be an object with number properties `latitude` and `longitude`.'\n    );\n  }\n\n  if (options?.useGoogleMaps || Platform.OS === 'web') {\n    if (__DEV__) {\n      console.warn(\n        'The Geocoding API has been removed in SDK 49, use Place Autocomplete service instead' +\n          '(https://developers.google.com/maps/documentation/places/web-service/autocomplete)'\n      );\n    }\n    return [];\n  }\n\n  return await ExpoLocation.reverseGeocodeAsync(location);\n}\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing location.\n * @return A promise that fulfills with an object of type [LocationPermissionResponse](#locationpermissionresponse).\n * @deprecated Use [`getForegroundPermissionsAsync`](#locationgetforegroundpermissionsasync) or [`getBackgroundPermissionsAsync`](#locationgetbackgroundpermissionsasync) instead.\n */\nexport async function getPermissionsAsync(): Promise<LocationPermissionResponse> {\n  console.warn(\n    `\"getPermissionsAsync()\" is now deprecated. Please use \"getForegroundPermissionsAsync()\" or \"getBackgroundPermissionsAsync()\" instead.`\n  );\n  return await ExpoLocation.getPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for location.\n * @return A promise that fulfills with an object of type [LocationPermissionResponse](#locationpermissionresponse).\n * @deprecated Use [`requestForegroundPermissionsAsync`](#locationrequestforegroundpermissionsasync) or [`requestBackgroundPermissionsAsync`](#locationrequestbackgroundpermissionsasync) instead.\n */\nexport async function requestPermissionsAsync(): Promise<LocationPermissionResponse> {\n  console.warn(\n    `\"requestPermissionsAsync()\" is now deprecated. Please use \"requestForegroundPermissionsAsync()\" or \"requestBackgroundPermissionsAsync()\" instead.`\n  );\n\n  return await ExpoLocation.requestPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing location while the app is in the foreground.\n * @return A promise that fulfills with an object of type [PermissionResponse](#permissionresponse).\n */\nexport async function getForegroundPermissionsAsync(): Promise<LocationPermissionResponse> {\n  return await ExpoLocation.getForegroundPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for location while the app is in the foreground.\n * @return A promise that fulfills with an object of type [PermissionResponse](#permissionresponse).\n */\nexport async function requestForegroundPermissionsAsync(): Promise<LocationPermissionResponse> {\n  return await ExpoLocation.requestForegroundPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Check or request permissions for the foreground location.\n * This uses both `requestForegroundPermissionsAsync` and `getForegroundPermissionsAsync` to interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = Location.useForegroundPermissions();\n * ```\n */\nexport const useForegroundPermissions = createPermissionHook({\n  getMethod: getForegroundPermissionsAsync,\n  requestMethod: requestForegroundPermissionsAsync,\n});\n\n// @needsAudit\n/**\n * Checks user's permissions for accessing location while the app is in the background.\n * @return A promise that fulfills with an object of type [PermissionResponse](#permissionresponse).\n */\nexport async function getBackgroundPermissionsAsync(): Promise<PermissionResponse> {\n  return await ExpoLocation.getBackgroundPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Asks the user to grant permissions for location while the app is in the background.\n * On __Android 11 or higher__: this method will open the system settings page - before that happens\n * you should explain to the user why your application needs background location permission.\n * For example, you can use `Modal` component from `react-native` to do that.\n * > __Note__: Foreground permissions should be granted before asking for the background permissions\n * (your app can't obtain background permission without foreground permission).\n * @return A promise that fulfills with an object of type [PermissionResponse](#permissionresponse).\n */\nexport async function requestBackgroundPermissionsAsync(): Promise<PermissionResponse> {\n  return await ExpoLocation.requestBackgroundPermissionsAsync();\n}\n\n// @needsAudit\n/**\n * Check or request permissions for the background location.\n * This uses both `requestBackgroundPermissionsAsync` and `getBackgroundPermissionsAsync` to\n * interact with the permissions.\n *\n * @example\n * ```ts\n * const [status, requestPermission] = Location.useBackgroundPermissions();\n * ```\n */\nexport const useBackgroundPermissions = createPermissionHook({\n  getMethod: getBackgroundPermissionsAsync,\n  requestMethod: requestBackgroundPermissionsAsync,\n});\n\n// --- Location service\n\n// @needsAudit\n/**\n * Checks whether location services are enabled by the user.\n * @return A promise which fulfills to `true` if location services are enabled on the device,\n * or `false` if not.\n */\nexport async function hasServicesEnabledAsync(): Promise<boolean> {\n  return await ExpoLocation.hasServicesEnabledAsync();\n}\n\n// --- Background location updates\n\nfunction _validateTaskName(taskName: string) {\n  if (!taskName || typeof taskName !== 'string') {\n    throw new Error(`\\`taskName\\` must be a non-empty string. Got ${taskName} instead.`);\n  }\n}\n\n// @docsMissing\nexport async function isBackgroundLocationAvailableAsync(): Promise<boolean> {\n  const providerStatus = await getProviderStatusAsync();\n  return providerStatus.backgroundModeEnabled;\n}\n\n// @needsAudit\n/**\n * Registers for receiving location updates that can also come when the app is in the background.\n *\n * # Task parameters\n *\n * Background location task will be receiving following data:\n * - `locations` - An array of the new locations.\n *\n * ```ts\n * import * as TaskManager from 'expo-task-manager';\n *\n * TaskManager.defineTask(YOUR_TASK_NAME, ({ data: { locations }, error }) => {\n *  if (error) {\n *    // check `error.message` for more details.\n *    return;\n *  }\n *  console.log('Received new locations', locations);\n * });\n * ```\n *\n * @param taskName Name of the task receiving location updates.\n * @param options An object of options passed to the location manager.\n *\n * @return A promise resolving once the task with location updates is registered.\n */\nexport async function startLocationUpdatesAsync(\n  taskName: string,\n  options: LocationTaskOptions = { accuracy: LocationAccuracy.Balanced }\n): Promise<void> {\n  _validateTaskName(taskName);\n  await ExpoLocation.startLocationUpdatesAsync(taskName, options);\n}\n\n// @needsAudit\n/**\n * Stops geofencing for specified task.\n * @param taskName Name of the background location task to stop.\n * @return A promise resolving as soon as the task is unregistered.\n */\nexport async function stopLocationUpdatesAsync(taskName: string): Promise<void> {\n  _validateTaskName(taskName);\n  await ExpoLocation.stopLocationUpdatesAsync(taskName);\n}\n\n// @needsAudit\n/**\n * @param taskName Name of the location task to check.\n * @return A promise which fulfills with boolean value indicating whether the location task is\n * started or not.\n */\nexport async function hasStartedLocationUpdatesAsync(taskName: string): Promise<boolean> {\n  _validateTaskName(taskName);\n  return ExpoLocation.hasStartedLocationUpdatesAsync(taskName);\n}\n\n// --- Geofencing\n\nfunction _validateRegions(regions: LocationRegion[]) {\n  if (!regions || regions.length === 0) {\n    throw new Error(\n      'Regions array cannot be empty. Use `stopGeofencingAsync` if you want to stop geofencing all regions'\n    );\n  }\n  for (const region of regions) {\n    if (typeof region.latitude !== 'number') {\n      throw new TypeError(`Region's latitude must be a number. Got '${region.latitude}' instead.`);\n    }\n    if (typeof region.longitude !== 'number') {\n      throw new TypeError(\n        `Region's longitude must be a number. Got '${region.longitude}' instead.`\n      );\n    }\n    if (typeof region.radius !== 'number') {\n      throw new TypeError(`Region's radius must be a number. Got '${region.radius}' instead.`);\n    }\n  }\n}\n\n// @needsAudit\n/**\n * Starts geofencing for given regions. When the new event comes, the task with specified name will\n * be called with the region that the device enter to or exit from.\n * If you want to add or remove regions from already running geofencing task, you can just call\n * `startGeofencingAsync` again with the new array of regions.\n *\n * # Task parameters\n *\n * Geofencing task will be receiving following data:\n *  - `eventType` - Indicates the reason for calling the task, which can be triggered by entering or exiting the region.\n *    See [GeofencingEventType](#geofencingeventtype).\n *  - `region` - Object containing details about updated region. See [LocationRegion](#locationregion) for more details.\n *\n * @param taskName Name of the task that will be called when the device enters or exits from specified regions.\n * @param regions Array of region objects to be geofenced.\n *\n * @return A promise resolving as soon as the task is registered.\n *\n * @example\n * ```ts\n * import { GeofencingEventType } from 'expo-location';\n * import * as TaskManager from 'expo-task-manager';\n *\n *  TaskManager.defineTask(YOUR_TASK_NAME, ({ data: { eventType, region }, error }) => {\n *   if (error) {\n *     // check `error.message` for more details.\n *     return;\n *   }\n *   if (eventType === GeofencingEventType.Enter) {\n *     console.log(\"You've entered region:\", region);\n *   } else if (eventType === GeofencingEventType.Exit) {\n *     console.log(\"You've left region:\", region);\n *   }\n * });\n * ```\n */\nexport async function startGeofencingAsync(\n  taskName: string,\n  regions: LocationRegion[] = []\n): Promise<void> {\n  _validateTaskName(taskName);\n  _validateRegions(regions);\n  await ExpoLocation.startGeofencingAsync(taskName, { regions });\n}\n\n// @needsAudit\n/**\n * Stops geofencing for specified task. It unregisters the background task so the app will not be\n * receiving any updates, especially in the background.\n * @param taskName Name of the task to unregister.\n * @return A promise resolving as soon as the task is unregistered.\n */\nexport async function stopGeofencingAsync(taskName: string): Promise<void> {\n  _validateTaskName(taskName);\n  await ExpoLocation.stopGeofencingAsync(taskName);\n}\n\n// @needsAudit\n/**\n * @param taskName Name of the geofencing task to check.\n * @return A promise which fulfills with boolean value indicating whether the geofencing task is\n * started or not.\n */\nexport async function hasStartedGeofencingAsync(taskName: string): Promise<boolean> {\n  _validateTaskName(taskName);\n  return ExpoLocation.hasStartedGeofencingAsync(taskName);\n}\n\nexport { LocationEventEmitter as EventEmitter, _getCurrentWatchId };\n\nexport {\n  LocationAccuracy as Accuracy,\n  LocationActivityType as ActivityType,\n  LocationGeofencingEventType as GeofencingEventType,\n  LocationGeofencingRegionState as GeofencingRegionState,\n  PermissionStatus,\n  PermissionHookOptions,\n  setGoogleApiKey,\n};\n\nexport { installWebGeolocationPolyfill } from './GeolocationPolyfill';\nexport * from './Location.types';\n"]}