{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"id\", \"initialRouteName\", \"children\", \"screenListeners\", \"screenOptions\"];\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { createNavigatorFactory, StackActions, StackRouter, useNavigationBuilder } from '@react-navigation/native';\nimport * as React from 'react';\nimport NativeStackView from \"../views/NativeStackView\";\nfunction NativeStackNavigator(_ref) {\n  var id = _ref.id,\n    initialRouteName = _ref.initialRouteName,\n    children = _ref.children,\n    screenListeners = _ref.screenListeners,\n    screenOptions = _ref.screenOptions,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _useNavigationBuilder = useNavigationBuilder(StackRouter, {\n      id: id,\n      initialRouteName: initialRouteName,\n      children: children,\n      screenListeners: screenListeners,\n      screenOptions: screenOptions\n    }),\n    state = _useNavigationBuilder.state,\n    descriptors = _useNavigationBuilder.descriptors,\n    navigation = _useNavigationBuilder.navigation,\n    NavigationContent = _useNavigationBuilder.NavigationContent;\n  React.useEffect(function () {\n    var _navigation$addListen;\n    return (navigation === null || navigation === void 0 ? void 0 : (_navigation$addListen = navigation.addListener) === null || _navigation$addListen === void 0 ? void 0 : _navigation$addListen.call(navigation, 'tabPress', function (e) {\n        var isFocused = navigation.isFocused();\n        requestAnimationFrame(function () {\n          if (state.index > 0 && isFocused && !e.defaultPrevented) {\n            navigation.dispatch(_objectSpread(_objectSpread({}, StackActions.popToTop()), {}, {\n              target: state.key\n            }));\n          }\n        });\n      })\n    );\n  }, [navigation, state.index, state.key]);\n  return React.createElement(NavigationContent, null, React.createElement(NativeStackView, _extends({}, rest, {\n    state: state,\n    navigation: navigation,\n    descriptors: descriptors\n  })));\n}\nexport default createNavigatorFactory(NativeStackNavigator);", "map": {"version": 3, "names": ["createNavigatorFactory", "StackActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useNavigationBuilder", "React", "NativeStackView", "NativeStackNavigator", "_ref", "id", "initialRouteName", "children", "screenListeners", "screenOptions", "rest", "_objectWithoutProperties", "_excluded", "_useNavigationBuilder", "state", "descriptors", "navigation", "NavigationContent", "useEffect", "_navigation$addListen", "addListener", "call", "e", "isFocused", "requestAnimationFrame", "index", "defaultPrevented", "dispatch", "_objectSpread", "popToTop", "target", "key", "createElement", "_extends"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native-stack\\src\\navigators\\createNativeStackNavigator.tsx"], "sourcesContent": ["import {\n  createNavigatorFactory,\n  EventArg,\n  ParamListBase,\n  StackActionHelpers,\n  StackActions,\n  StackNavigationState,\n  StackRouter,\n  StackRouterOptions,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport * as React from 'react';\n\nimport type {\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  NativeStackNavigatorProps,\n} from '../types';\nimport NativeStackView from '../views/NativeStackView';\n\nfunction NativeStackNavigator({\n  id,\n  initialRouteName,\n  children,\n  screenListeners,\n  screenOptions,\n  ...rest\n}: NativeStackNavigatorProps) {\n  const { state, descriptors, navigation, NavigationContent } =\n    useNavigationBuilder<\n      StackNavigationState<ParamListBase>,\n      StackRouterOptions,\n      StackActionHelpers<ParamListBase>,\n      NativeStackNavigationOptions,\n      NativeStackNavigationEventMap\n    >(StackRouter, {\n      id,\n      initialRouteName,\n      children,\n      screenListeners,\n      screenOptions,\n    });\n\n  React.useEffect(\n    () =>\n      // @ts-expect-error: there may not be a tab navigator in parent\n      navigation?.addListener?.('tabPress', (e: any) => {\n        const isFocused = navigation.isFocused();\n\n        // Run the operation in the next frame so we're sure all listeners have been run\n        // This is necessary to know if preventDefault() has been called\n        requestAnimationFrame(() => {\n          if (\n            state.index > 0 &&\n            isFocused &&\n            !(e as EventArg<'tabPress', true>).defaultPrevented\n          ) {\n            // When user taps on already focused tab and we're inside the tab,\n            // reset the stack to replicate native behaviour\n            navigation.dispatch({\n              ...StackActions.popToTop(),\n              target: state.key,\n            });\n          }\n        });\n      }),\n    [navigation, state.index, state.key]\n  );\n\n  return (\n    <NavigationContent>\n      <NativeStackView\n        {...rest}\n        state={state}\n        navigation={navigation}\n        descriptors={descriptors}\n      />\n    </NavigationContent>\n  );\n}\n\nexport default createNavigatorFactory<\n  StackNavigationState<ParamListBase>,\n  NativeStackNavigationOptions,\n  NativeStackNavigationEventMap,\n  typeof NativeStackNavigator\n>(NativeStackNavigator);\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,SACEA,sBAAsB,EAItBC,YAAY,EAEZC,WAAW,EAEXC,oBAAoB,QACf,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAO9B,OAAOC,eAAe;AAEtB,SAASC,oBAAoBA,CAAAC,IAAA,EAOC;EAAA,IAN5BC,EAAE,GAMwBD,IAAA,CAN1BC,EAAE;IACFC,gBAAgB,GAKUF,IAAA,CAL1BE,gBAAgB;IAChBC,QAAQ,GAIkBH,IAAA,CAJ1BG,QAAQ;IACRC,eAAe,GAGWJ,IAAA,CAH1BI,eAAe;IACfC,aAAa,GAEaL,IAAA,CAF1BK,aAAa;IACVC,IAAA,GAAAC,wBAAA,CACuBP,IAAA,EAAAQ,SAAA;EAC1B,IAAAC,qBAAA,GACEb,oBAAoB,CAMlBD,WAAW,EAAE;MACbM,EAAE,EAAFA,EAAE;MACFC,gBAAgB,EAAhBA,gBAAgB;MAChBC,QAAQ,EAARA,QAAQ;MACRC,eAAe,EAAfA,eAAe;MACfC,aAAA,EAAAA;IACF,CAAC,CAAC;IAbIK,KAAK,GAAAD,qBAAA,CAALC,KAAK;IAAEC,WAAW,GAAAF,qBAAA,CAAXE,WAAW;IAAEC,UAAU,GAAAH,qBAAA,CAAVG,UAAU;IAAEC,iBAAA,GAAAJ,qBAAA,CAAAI,iBAAA;EAexChB,KAAK,CAACiB,SAAS,CACb;IAAA,IAAAC,qBAAA;IAAA,QAEEH,UAAU,aAAVA,UAAU,wBAAAG,qBAAA,GAAVH,UAAU,CAAEI,WAAW,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAAE,IAAA,CAAAL,UAAU,EAAgB,UAAU,EAAG,UAAAM,CAAM,EAAK;QAChD,IAAMC,SAAS,GAAGP,UAAU,CAACO,SAAS,EAAE;QAIxCC,qBAAqB,CAAC,YAAM;UAC1B,IACEV,KAAK,CAACW,KAAK,GAAG,CAAC,IACfF,SAAS,IACT,CAAED,CAAC,CAAgCI,gBAAgB,EACnD;YAGAV,UAAU,CAACW,QAAQ,CAAAC,aAAA,CAAAA,aAAA,KACd9B,YAAY,CAAC+B,QAAQ,EAAE;cAC1BC,MAAM,EAAEhB,KAAK,CAACiB;YAAA,EACf,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC;IAAA;EAAC,GACJ,CAACf,UAAU,EAAEF,KAAK,CAACW,KAAK,EAAEX,KAAK,CAACiB,GAAG,CAAC,CACrC;EAED,OACE9B,KAAA,CAAA+B,aAAA,CAACf,iBAAiB,QAChBhB,KAAA,CAAA+B,aAAA,CAAC9B,eAAe,EAAA+B,QAAA,KACVvB,IAAI;IACRI,KAAK,EAAEA,KAAM;IACbE,UAAU,EAAEA,UAAW;IACvBD,WAAW,EAAEA;EAAY,GACzB,CACgB;AAExB;AAEA,eAAelB,sBAAsB,CAKnCM,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}