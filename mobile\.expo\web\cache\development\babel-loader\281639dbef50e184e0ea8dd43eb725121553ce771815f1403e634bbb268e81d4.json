{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { useNavigation } from '@react-navigation/native';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar HomeScreen = function HomeScreen() {\n  var navigation = useNavigation();\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"Welcome to GasLink\"\n    }), _jsx(Text, {\n      style: styles.subtitle,\n      children: \"Find the best gas stations near you\"\n    }), _jsx(TouchableOpacity, {\n      style: styles.button,\n      onPress: function onPress() {\n        return navigation.navigate('Search');\n      },\n      children: _jsx(Text, {\n        style: styles.buttonText,\n        children: \"Search Gas Stations\"\n      })\n    }), _jsx(TouchableOpacity, {\n      style: styles.button,\n      onPress: function onPress() {\n        return navigation.navigate('Profile');\n      },\n      children: _jsx(Text, {\n        style: styles.buttonText,\n        children: \"My Profile\"\n      })\n    }), _jsx(TouchableOpacity, {\n      style: styles.button,\n      onPress: function onPress() {\n        return navigation.navigate('Login');\n      },\n      children: _jsx(Text, {\n        style: styles.buttonText,\n        children: \"Login\"\n      })\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 10,\n    textAlign: 'center'\n  },\n  subtitle: {\n    fontSize: 16,\n    marginBottom: 30,\n    textAlign: 'center',\n    color: '#666'\n  },\n  button: {\n    backgroundColor: '#007bff',\n    padding: 15,\n    borderRadius: 8,\n    marginVertical: 10,\n    width: '80%',\n    alignItems: 'center'\n  },\n  buttonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600'\n  }\n});\nexport default HomeScreen;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "TouchableOpacity", "useNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "HomeScreen", "navigation", "style", "styles", "container", "children", "title", "subtitle", "button", "onPress", "navigate", "buttonText", "create", "flex", "justifyContent", "alignItems", "padding", "backgroundColor", "fontSize", "fontWeight", "marginBottom", "textAlign", "color", "borderRadius", "marginVertical", "width"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/screens/HomeScreen.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity } from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\n\nconst HomeScreen = () => {\n  const navigation = useNavigation();\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Welcome to GasLink</Text>\n      <Text style={styles.subtitle}>Find the best gas stations near you</Text>\n      \n      <TouchableOpacity \n        style={styles.button}\n        onPress={() => navigation.navigate('Search')}\n      >\n        <Text style={styles.buttonText}>Search Gas Stations</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity \n        style={styles.button}\n        onPress={() => navigation.navigate('Profile')}\n      >\n        <Text style={styles.buttonText}>My Profile</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity \n        style={styles.button}\n        onPress={() => navigation.navigate('Login')}\n      >\n        <Text style={styles.buttonText}>Login</Text>\n      </TouchableOpacity>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 10,\n    textAlign: 'center',\n  },\n  subtitle: {\n    fontSize: 16,\n    marginBottom: 30,\n    textAlign: 'center',\n    color: '#666',\n  },\n  button: {\n    backgroundColor: '#007bff',\n    padding: 15,\n    borderRadius: 8,\n    marginVertical: 10,\n    width: '80%',\n    alignItems: 'center',\n  },\n  buttonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n});\n\nexport default HomeScreen;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAE1B,SAASC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzD,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EACvB,IAAMC,UAAU,GAAGN,aAAa,CAAC,CAAC;EAElC,OACEI,KAAA,CAACR,IAAI;IAACW,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BR,IAAA,CAACL,IAAI;MAACU,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAkB,CAAM,CAAC,EACpDR,IAAA,CAACL,IAAI;MAACU,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAAC;IAAmC,CAAM,CAAC,EAExER,IAAA,CAACH,gBAAgB;MACfQ,KAAK,EAAEC,MAAM,CAACK,MAAO;MACrBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQR,UAAU,CAACS,QAAQ,CAAC,QAAQ,CAAC;MAAA,CAAC;MAAAL,QAAA,EAE7CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACQ,UAAW;QAAAN,QAAA,EAAC;MAAmB,CAAM;IAAC,CAC1C,CAAC,EAEnBR,IAAA,CAACH,gBAAgB;MACfQ,KAAK,EAAEC,MAAM,CAACK,MAAO;MACrBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQR,UAAU,CAACS,QAAQ,CAAC,SAAS,CAAC;MAAA,CAAC;MAAAL,QAAA,EAE9CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACQ,UAAW;QAAAN,QAAA,EAAC;MAAU,CAAM;IAAC,CACjC,CAAC,EAEnBR,IAAA,CAACH,gBAAgB;MACfQ,KAAK,EAAEC,MAAM,CAACK,MAAO;MACrBC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQR,UAAU,CAACS,QAAQ,CAAC,OAAO,CAAC;MAAA,CAAC;MAAAL,QAAA,EAE5CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACQ,UAAW;QAAAN,QAAA,EAAC;MAAK,CAAM;IAAC,CAC5B,CAAC;EAAA,CACf,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGV,UAAU,CAACmB,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDX,KAAK,EAAE;IACLY,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDd,QAAQ,EAAE;IACRW,QAAQ,EAAE,EAAE;IACZE,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,QAAQ;IACnBC,KAAK,EAAE;EACT,CAAC;EACDd,MAAM,EAAE;IACNS,eAAe,EAAE,SAAS;IAC1BD,OAAO,EAAE,EAAE;IACXO,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE,KAAK;IACZV,UAAU,EAAE;EACd,CAAC;EACDJ,UAAU,EAAE;IACVW,KAAK,EAAE,OAAO;IACdJ,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAenB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}