{"ast": null, "code": "import * as React from 'react';\nimport ThemeContext from \"./ThemeContext\";\nexport default function useTheme() {\n  var theme = React.useContext(ThemeContext);\n  return theme;\n}", "map": {"version": 3, "names": ["React", "ThemeContext", "useTheme", "theme", "useContext"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native\\src\\theming\\useTheme.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport ThemeContext from './ThemeContext';\n\nexport default function useTheme() {\n  const theme = React.useContext(ThemeContext);\n\n  return theme;\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,YAAY;AAEnB,eAAe,SAASC,QAAQA,CAAA,EAAG;EACjC,IAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,YAAY,CAAC;EAE5C,OAAOE,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}