{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { useNavigation } from '@react-navigation/native';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ProfileScreen = function ProfileScreen() {\n  var navigation = useNavigation();\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"My Profile\"\n    }), _jsxs(View, {\n      style: styles.profileInfo,\n      children: [_jsx(Text, {\n        style: styles.infoLabel,\n        children: \"Name:\"\n      }), _jsx(Text, {\n        style: styles.infoValue,\n        children: \"John Doe\"\n      }), _jsx(Text, {\n        style: styles.infoLabel,\n        children: \"Email:\"\n      }), _jsx(Text, {\n        style: styles.infoValue,\n        children: \"<EMAIL>\"\n      }), _jsx(Text, {\n        style: styles.infoLabel,\n        children: \"Member since:\"\n      }), _jsx(Text, {\n        style: styles.infoValue,\n        children: \"January 2024\"\n      })]\n    }), _jsx(TouchableOpacity, {\n      style: styles.button,\n      children: _jsx(Text, {\n        style: styles.buttonText,\n        children: \"Edit Profile\"\n      })\n    }), _jsx(TouchableOpacity, {\n      style: styles.button,\n      children: _jsx(Text, {\n        style: styles.buttonText,\n        children: \"My Reviews\"\n      })\n    }), _jsx(TouchableOpacity, {\n      style: styles.button,\n      children: _jsx(Text, {\n        style: styles.buttonText,\n        children: \"Settings\"\n      })\n    }), _jsx(TouchableOpacity, {\n      style: styles.logoutButton,\n      children: _jsx(Text, {\n        style: styles.logoutButtonText,\n        children: \"Logout\"\n      })\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 20,\n    backgroundColor: '#f5f5f5'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 20,\n    textAlign: 'center'\n  },\n  profileInfo: {\n    backgroundColor: 'white',\n    padding: 20,\n    borderRadius: 8,\n    marginBottom: 20\n  },\n  infoLabel: {\n    fontSize: 16,\n    fontWeight: '600',\n    marginBottom: 5\n  },\n  infoValue: {\n    fontSize: 16,\n    color: '#666',\n    marginBottom: 15\n  },\n  button: {\n    backgroundColor: '#007bff',\n    padding: 15,\n    borderRadius: 8,\n    marginVertical: 5,\n    alignItems: 'center'\n  },\n  buttonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600'\n  },\n  logoutButton: {\n    backgroundColor: '#dc3545',\n    padding: 15,\n    borderRadius: 8,\n    marginVertical: 5,\n    alignItems: 'center'\n  },\n  logoutButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600'\n  }\n});\nexport default ProfileScreen;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "TouchableOpacity", "useNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "ProfileScreen", "navigation", "style", "styles", "container", "children", "title", "profileInfo", "infoLabel", "infoValue", "button", "buttonText", "logoutButton", "logoutButtonText", "create", "flex", "padding", "backgroundColor", "fontSize", "fontWeight", "marginBottom", "textAlign", "borderRadius", "color", "marginVertical", "alignItems"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/screens/ProfileScreen.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet, TouchableOpacity } from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\n\nconst ProfileScreen = () => {\n  const navigation = useNavigation();\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>My Profile</Text>\n      \n      <View style={styles.profileInfo}>\n        <Text style={styles.infoLabel}>Name:</Text>\n        <Text style={styles.infoValue}>John <PERSON>e</Text>\n        \n        <Text style={styles.infoLabel}>Email:</Text>\n        <Text style={styles.infoValue}><EMAIL></Text>\n        \n        <Text style={styles.infoLabel}>Member since:</Text>\n        <Text style={styles.infoValue}>January 2024</Text>\n      </View>\n\n      <TouchableOpacity style={styles.button}>\n        <Text style={styles.buttonText}>Edit Profile</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity style={styles.button}>\n        <Text style={styles.buttonText}>My Reviews</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity style={styles.button}>\n        <Text style={styles.buttonText}>Settings</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity style={styles.logoutButton}>\n        <Text style={styles.logoutButtonText}>Logout</Text>\n      </TouchableOpacity>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    padding: 20,\n    backgroundColor: '#f5f5f5',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 20,\n    textAlign: 'center',\n  },\n  profileInfo: {\n    backgroundColor: 'white',\n    padding: 20,\n    borderRadius: 8,\n    marginBottom: 20,\n  },\n  infoLabel: {\n    fontSize: 16,\n    fontWeight: '600',\n    marginBottom: 5,\n  },\n  infoValue: {\n    fontSize: 16,\n    color: '#666',\n    marginBottom: 15,\n  },\n  button: {\n    backgroundColor: '#007bff',\n    padding: 15,\n    borderRadius: 8,\n    marginVertical: 5,\n    alignItems: 'center',\n  },\n  buttonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n  logoutButton: {\n    backgroundColor: '#dc3545',\n    padding: 15,\n    borderRadius: 8,\n    marginVertical: 5,\n    alignItems: 'center',\n  },\n  logoutButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n});\n\nexport default ProfileScreen;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,gBAAA;AAE1B,SAASC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAC1B,IAAMC,UAAU,GAAGN,aAAa,CAAC,CAAC;EAElC,OACEI,KAAA,CAACR,IAAI;IAACW,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BR,IAAA,CAACL,IAAI;MAACU,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAU,CAAM,CAAC,EAE5CN,KAAA,CAACR,IAAI;MAACW,KAAK,EAAEC,MAAM,CAACI,WAAY;MAAAF,QAAA,GAC9BR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACK,SAAU;QAAAH,QAAA,EAAC;MAAK,CAAM,CAAC,EAC3CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EAAC;MAAQ,CAAM,CAAC,EAE9CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACK,SAAU;QAAAH,QAAA,EAAC;MAAM,CAAM,CAAC,EAC5CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EAAC;MAAoB,CAAM,CAAC,EAE1DR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACK,SAAU;QAAAH,QAAA,EAAC;MAAa,CAAM,CAAC,EACnDR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACM,SAAU;QAAAJ,QAAA,EAAC;MAAY,CAAM,CAAC;IAAA,CAC9C,CAAC,EAEPR,IAAA,CAACH,gBAAgB;MAACQ,KAAK,EAAEC,MAAM,CAACO,MAAO;MAAAL,QAAA,EACrCR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACQ,UAAW;QAAAN,QAAA,EAAC;MAAY,CAAM;IAAC,CACnC,CAAC,EAEnBR,IAAA,CAACH,gBAAgB;MAACQ,KAAK,EAAEC,MAAM,CAACO,MAAO;MAAAL,QAAA,EACrCR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACQ,UAAW;QAAAN,QAAA,EAAC;MAAU,CAAM;IAAC,CACjC,CAAC,EAEnBR,IAAA,CAACH,gBAAgB;MAACQ,KAAK,EAAEC,MAAM,CAACO,MAAO;MAAAL,QAAA,EACrCR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACQ,UAAW;QAAAN,QAAA,EAAC;MAAQ,CAAM;IAAC,CAC/B,CAAC,EAEnBR,IAAA,CAACH,gBAAgB;MAACQ,KAAK,EAAEC,MAAM,CAACS,YAAa;MAAAP,QAAA,EAC3CR,IAAA,CAACL,IAAI;QAACU,KAAK,EAAEC,MAAM,CAACU,gBAAiB;QAAAR,QAAA,EAAC;MAAM,CAAM;IAAC,CACnC,CAAC;EAAA,CACf,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGV,UAAU,CAACqB,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,IAAI,EAAE,CAAC;IACPC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDX,KAAK,EAAE;IACLY,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDd,WAAW,EAAE;IACXU,eAAe,EAAE,OAAO;IACxBD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfF,YAAY,EAAE;EAChB,CAAC;EACDZ,SAAS,EAAE;IACTU,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAChB,CAAC;EACDX,SAAS,EAAE;IACTS,QAAQ,EAAE,EAAE;IACZK,KAAK,EAAE,MAAM;IACbH,YAAY,EAAE;EAChB,CAAC;EACDV,MAAM,EAAE;IACNO,eAAe,EAAE,SAAS;IAC1BD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfE,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE;EACd,CAAC;EACDd,UAAU,EAAE;IACVY,KAAK,EAAE,OAAO;IACdL,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDP,YAAY,EAAE;IACZK,eAAe,EAAE,SAAS;IAC1BD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfE,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE;EACd,CAAC;EACDZ,gBAAgB,EAAE;IAChBU,KAAK,EAAE,OAAO;IACdL,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAenB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}