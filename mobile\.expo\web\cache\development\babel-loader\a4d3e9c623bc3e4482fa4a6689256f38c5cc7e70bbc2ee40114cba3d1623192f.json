{"ast": null, "code": "import React from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar GasStationDetailScreen = function GasStationDetailScreen(_ref) {\n  var route = _ref.route;\n  var _ref2 = route.params || {},\n    gasStation = _ref2.gasStation;\n  return _jsxs(ScrollView, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Text, {\n        style: styles.title,\n        children: (gasStation == null ? void 0 : gasStation.name) || 'Gas Station'\n      }), _jsx(Text, {\n        style: styles.address,\n        children: (gasStation == null ? void 0 : gasStation.address) || 'Address not available'\n      })]\n    }), _jsxs(View, {\n      style: styles.section,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Fuel Prices\"\n      }), _jsx(Text, {\n        style: styles.infoText,\n        children: \"Prices not available\"\n      })]\n    }), _jsxs(View, {\n      style: styles.section,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Amenities\"\n      }), _jsx(Text, {\n        style: styles.infoText,\n        children: \"No amenities listed\"\n      })]\n    }), _jsxs(View, {\n      style: styles.section,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Operating Hours\"\n      }), _jsx(Text, {\n        style: styles.infoText,\n        children: \"Hours not specified\"\n      })]\n    }), _jsxs(View, {\n      style: styles.section,\n      children: [_jsx(Text, {\n        style: styles.sectionTitle,\n        children: \"Reviews\"\n      }), _jsx(Text, {\n        style: styles.infoText,\n        children: \"No reviews yet\"\n      })]\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  header: {\n    backgroundColor: 'white',\n    padding: 20,\n    marginBottom: 10\n  },\n  title: {\n    fontSize: 22,\n    fontWeight: 'bold',\n    marginBottom: 5\n  },\n  address: {\n    fontSize: 16,\n    color: '#666'\n  },\n  section: {\n    backgroundColor: 'white',\n    padding: 15,\n    marginBottom: 10\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: '600',\n    marginBottom: 10\n  },\n  infoText: {\n    fontSize: 16,\n    color: '#999',\n    fontStyle: 'italic'\n  }\n});\nexport default GasStationDetailScreen;", "map": {"version": 3, "names": ["React", "View", "Text", "StyleSheet", "ScrollView", "jsx", "_jsx", "jsxs", "_jsxs", "GasStationDetailScreen", "_ref", "route", "_ref2", "params", "gasStation", "style", "styles", "container", "children", "header", "title", "name", "address", "section", "sectionTitle", "infoText", "create", "flex", "backgroundColor", "padding", "marginBottom", "fontSize", "fontWeight", "color", "fontStyle"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/screens/GasStationDetailScreen.js"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet, ScrollView } from 'react-native';\n\nconst GasStationDetailScreen = ({ route }) => {\n  const { gasStation } = route.params || {};\n\n  return (\n    <ScrollView style={styles.container}>\n      <View style={styles.header}>\n        <Text style={styles.title}>{gasStation?.name || 'Gas Station'}</Text>\n        <Text style={styles.address}>{gasStation?.address || 'Address not available'}</Text>\n      </View>\n\n      <View style={styles.section}>\n        <Text style={styles.sectionTitle}>Fuel Prices</Text>\n        {/* Fuel prices would be displayed here */}\n        <Text style={styles.infoText}>Prices not available</Text>\n      </View>\n\n      <View style={styles.section}>\n        <Text style={styles.sectionTitle}>Amenities</Text>\n        {/* Amenities would be listed here */}\n        <Text style={styles.infoText}>No amenities listed</Text>\n      </View>\n\n      <View style={styles.section}>\n        <Text style={styles.sectionTitle}>Operating Hours</Text>\n        {/* Operating hours would be displayed here */}\n        <Text style={styles.infoText}>Hours not specified</Text>\n      </View>\n\n      <View style={styles.section}>\n        <Text style={styles.sectionTitle}>Reviews</Text>\n        {/* Reviews would be listed here */}\n        <Text style={styles.infoText}>No reviews yet</Text>\n      </View>\n    </ScrollView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  header: {\n    backgroundColor: 'white',\n    padding: 20,\n    marginBottom: 10,\n  },\n  title: {\n    fontSize: 22,\n    fontWeight: 'bold',\n    marginBottom: 5,\n  },\n  address: {\n    fontSize: 16,\n    color: '#666',\n  },\n  section: {\n    backgroundColor: 'white',\n    padding: 15,\n    marginBottom: 10,\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: '600',\n    marginBottom: 10,\n  },\n  infoText: {\n    fontSize: 16,\n    color: '#999',\n    fontStyle: 'italic',\n  },\n});\n\nexport default GasStationDetailScreen;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAG1B,IAAMC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAAC,IAAA,EAAkB;EAAA,IAAZC,KAAK,GAAAD,IAAA,CAALC,KAAK;EACrC,IAAAC,KAAA,GAAuBD,KAAK,CAACE,MAAM,IAAI,CAAC,CAAC;IAAjCC,UAAU,GAAAF,KAAA,CAAVE,UAAU;EAElB,OACEN,KAAA,CAACJ,UAAU;IAACW,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAClCV,KAAA,CAACP,IAAI;MAACc,KAAK,EAAEC,MAAM,CAACG,MAAO;MAAAD,QAAA,GACzBZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACI,KAAM;QAAAF,QAAA,EAAE,CAAAJ,UAAU,oBAAVA,UAAU,CAAEO,IAAI,KAAI;MAAa,CAAO,CAAC,EACrEf,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACM,OAAQ;QAAAJ,QAAA,EAAE,CAAAJ,UAAU,oBAAVA,UAAU,CAAEQ,OAAO,KAAI;MAAuB,CAAO,CAAC;IAAA,CAChF,CAAC,EAEPd,KAAA,CAACP,IAAI;MAACc,KAAK,EAAEC,MAAM,CAACO,OAAQ;MAAAL,QAAA,GAC1BZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACQ,YAAa;QAAAN,QAAA,EAAC;MAAW,CAAM,CAAC,EAEpDZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACS,QAAS;QAAAP,QAAA,EAAC;MAAoB,CAAM,CAAC;IAAA,CACrD,CAAC,EAEPV,KAAA,CAACP,IAAI;MAACc,KAAK,EAAEC,MAAM,CAACO,OAAQ;MAAAL,QAAA,GAC1BZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACQ,YAAa;QAAAN,QAAA,EAAC;MAAS,CAAM,CAAC,EAElDZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACS,QAAS;QAAAP,QAAA,EAAC;MAAmB,CAAM,CAAC;IAAA,CACpD,CAAC,EAEPV,KAAA,CAACP,IAAI;MAACc,KAAK,EAAEC,MAAM,CAACO,OAAQ;MAAAL,QAAA,GAC1BZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACQ,YAAa;QAAAN,QAAA,EAAC;MAAe,CAAM,CAAC,EAExDZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACS,QAAS;QAAAP,QAAA,EAAC;MAAmB,CAAM,CAAC;IAAA,CACpD,CAAC,EAEPV,KAAA,CAACP,IAAI;MAACc,KAAK,EAAEC,MAAM,CAACO,OAAQ;MAAAL,QAAA,GAC1BZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACQ,YAAa;QAAAN,QAAA,EAAC;MAAO,CAAM,CAAC,EAEhDZ,IAAA,CAACJ,IAAI;QAACa,KAAK,EAAEC,MAAM,CAACS,QAAS;QAAAP,QAAA,EAAC;MAAc,CAAM,CAAC;IAAA,CAC/C,CAAC;EAAA,CACG,CAAC;AAEjB,CAAC;AAED,IAAMF,MAAM,GAAGb,UAAU,CAACuB,MAAM,CAAC;EAC/BT,SAAS,EAAE;IACTU,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDT,MAAM,EAAE;IACNS,eAAe,EAAE,OAAO;IACxBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE;EAChB,CAAC;EACDV,KAAK,EAAE;IACLW,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBF,YAAY,EAAE;EAChB,CAAC;EACDR,OAAO,EAAE;IACPS,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE;EACT,CAAC;EACDV,OAAO,EAAE;IACPK,eAAe,EAAE,OAAO;IACxBC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE;EAChB,CAAC;EACDN,YAAY,EAAE;IACZO,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,KAAK;IACjBF,YAAY,EAAE;EAChB,CAAC;EACDL,QAAQ,EAAE;IACRM,QAAQ,EAAE,EAAE;IACZE,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAezB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}