{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport TextInput from \"react-native-web/dist/exports/TextInput\";\nimport TouchableOpacity from \"react-native-web/dist/exports/TouchableOpacity\";\nimport { useNavigation } from '@react-navigation/native';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar LoginScreen = function LoginScreen() {\n  var _useState = useState(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    email = _useState2[0],\n    setEmail = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    password = _useState4[0],\n    setPassword = _useState4[1];\n  var navigation = useNavigation();\n  var handleLogin = function handleLogin() {\n    console.log('Login attempt with:', email, password);\n    navigation.navigate('Home');\n  };\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"Login\"\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Email\",\n      value: email,\n      onChangeText: setEmail,\n      keyboardType: \"email-address\",\n      autoCapitalize: \"none\"\n    }), _jsx(TextInput, {\n      style: styles.input,\n      placeholder: \"Password\",\n      value: password,\n      onChangeText: setPassword,\n      secureTextEntry: true\n    }), _jsx(TouchableOpacity, {\n      style: styles.loginButton,\n      onPress: handleLogin,\n      children: _jsx(Text, {\n        style: styles.loginButtonText,\n        children: \"Login\"\n      })\n    }), _jsx(TouchableOpacity, {\n      onPress: function onPress() {\n        return navigation.navigate('Register');\n      },\n      children: _jsx(Text, {\n        style: styles.registerLink,\n        children: \"Don't have an account? Register here\"\n      })\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5'\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 30,\n    textAlign: 'center'\n  },\n  input: {\n    backgroundColor: 'white',\n    padding: 15,\n    borderRadius: 8,\n    marginBottom: 15,\n    borderWidth: 1,\n    borderColor: '#ddd'\n  },\n  loginButton: {\n    backgroundColor: '#007bff',\n    padding: 15,\n    borderRadius: 8,\n    alignItems: 'center',\n    marginBottom: 20\n  },\n  loginButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600'\n  },\n  registerLink: {\n    textAlign: 'center',\n    color: '#007bff',\n    fontSize: 16\n  }\n});\nexport default LoginScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "Text", "StyleSheet", "TextInput", "TouchableOpacity", "useNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "LoginScreen", "_useState", "_useState2", "_slicedToArray", "email", "setEmail", "_useState3", "_useState4", "password", "setPassword", "navigation", "handleLogin", "console", "log", "navigate", "style", "styles", "container", "children", "title", "input", "placeholder", "value", "onChangeText", "keyboardType", "autoCapitalize", "secureTextEntry", "loginButton", "onPress", "loginButtonText", "registerLink", "create", "flex", "justifyContent", "padding", "backgroundColor", "fontSize", "fontWeight", "marginBottom", "textAlign", "borderRadius", "borderWidth", "borderColor", "alignItems", "color"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/screens/LoginScreen.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';\nimport { useNavigation } from '@react-navigation/native';\n\nconst LoginScreen = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const navigation = useNavigation();\n\n  const handleLogin = () => {\n    // Login logic will be implemented later\n    console.log('Login attempt with:', email, password);\n    // For now, just navigate to Home\n    navigation.navigate('Home');\n  };\n\n  return (\n    <View style={styles.container}>\n      <Text style={styles.title}>Login</Text>\n      \n      <TextInput\n        style={styles.input}\n        placeholder=\"Email\"\n        value={email}\n        onChangeText={setEmail}\n        keyboardType=\"email-address\"\n        autoCapitalize=\"none\"\n      />\n      \n      <TextInput\n        style={styles.input}\n        placeholder=\"Password\"\n        value={password}\n        onChangeText={setPassword}\n        secureTextEntry\n      />\n      \n      <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>\n        <Text style={styles.loginButtonText}>Login</Text>\n      </TouchableOpacity>\n\n      <TouchableOpacity onPress={() => navigation.navigate('Register')}>\n        <Text style={styles.registerLink}>\n          Don't have an account? Register here\n        </Text>\n      </TouchableOpacity>\n    </View>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n    backgroundColor: '#f5f5f5',\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 30,\n    textAlign: 'center',\n  },\n  input: {\n    backgroundColor: 'white',\n    padding: 15,\n    borderRadius: 8,\n    marginBottom: 15,\n    borderWidth: 1,\n    borderColor: '#ddd',\n  },\n  loginButton: {\n    backgroundColor: '#007bff',\n    padding: 15,\n    borderRadius: 8,\n    alignItems: 'center',\n    marginBottom: 20,\n  },\n  loginButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n  registerLink: {\n    textAlign: 'center',\n    color: '#007bff',\n    fontSize: 16,\n  },\n});\n\nexport default LoginScreen;"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,SAAA;AAAA,OAAAC,gBAAA;AAExC,SAASC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEzD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;EACxB,IAAAC,SAAA,GAA0BZ,QAAQ,CAAC,EAAE,CAAC;IAAAa,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA/BG,KAAK,GAAAF,UAAA;IAAEG,QAAQ,GAAAH,UAAA;EACtB,IAAAI,UAAA,GAAgCjB,QAAQ,CAAC,EAAE,CAAC;IAAAkB,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAArCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAMG,UAAU,GAAGf,aAAa,CAAC,CAAC;EAElC,IAAMgB,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IAExBC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAET,KAAK,EAAEI,QAAQ,CAAC;IAEnDE,UAAU,CAACI,QAAQ,CAAC,MAAM,CAAC;EAC7B,CAAC;EAED,OACEf,KAAA,CAACT,IAAI;IAACyB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BrB,IAAA,CAACN,IAAI;MAACwB,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAK,CAAM,CAAC,EAEvCrB,IAAA,CAACJ,SAAS;MACRsB,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,OAAO;MACnBC,KAAK,EAAElB,KAAM;MACbmB,YAAY,EAAElB,QAAS;MACvBmB,YAAY,EAAC,eAAe;MAC5BC,cAAc,EAAC;IAAM,CACtB,CAAC,EAEF5B,IAAA,CAACJ,SAAS;MACRsB,KAAK,EAAEC,MAAM,CAACI,KAAM;MACpBC,WAAW,EAAC,UAAU;MACtBC,KAAK,EAAEd,QAAS;MAChBe,YAAY,EAAEd,WAAY;MAC1BiB,eAAe;IAAA,CAChB,CAAC,EAEF7B,IAAA,CAACH,gBAAgB;MAACqB,KAAK,EAAEC,MAAM,CAACW,WAAY;MAACC,OAAO,EAAEjB,WAAY;MAAAO,QAAA,EAChErB,IAAA,CAACN,IAAI;QAACwB,KAAK,EAAEC,MAAM,CAACa,eAAgB;QAAAX,QAAA,EAAC;MAAK,CAAM;IAAC,CACjC,CAAC,EAEnBrB,IAAA,CAACH,gBAAgB;MAACkC,OAAO,EAAE,SAATA,OAAOA,CAAA;QAAA,OAAQlB,UAAU,CAACI,QAAQ,CAAC,UAAU,CAAC;MAAA,CAAC;MAAAI,QAAA,EAC/DrB,IAAA,CAACN,IAAI;QAACwB,KAAK,EAAEC,MAAM,CAACc,YAAa;QAAAZ,QAAA,EAAC;MAElC,CAAM;IAAC,CACS,CAAC;EAAA,CACf,CAAC;AAEX,CAAC;AAED,IAAMF,MAAM,GAAGxB,UAAU,CAACuC,MAAM,CAAC;EAC/Bd,SAAS,EAAE;IACTe,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE,EAAE;IACXC,eAAe,EAAE;EACnB,CAAC;EACDhB,KAAK,EAAE;IACLiB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDnB,KAAK,EAAE;IACLe,eAAe,EAAE,OAAO;IACxBD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfF,YAAY,EAAE,EAAE;IAChBG,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE;EACf,CAAC;EACDf,WAAW,EAAE;IACXQ,eAAe,EAAE,SAAS;IAC1BD,OAAO,EAAE,EAAE;IACXM,YAAY,EAAE,CAAC;IACfG,UAAU,EAAE,QAAQ;IACpBL,YAAY,EAAE;EAChB,CAAC;EACDT,eAAe,EAAE;IACfe,KAAK,EAAE,OAAO;IACdR,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDP,YAAY,EAAE;IACZS,SAAS,EAAE,QAAQ;IACnBK,KAAK,EAAE,SAAS;IAChBR,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,eAAepC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}