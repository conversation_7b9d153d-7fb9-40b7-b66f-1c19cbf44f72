{"name": "@react-native-community/cli-clean", "version": "11.3.10", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-tools": "11.3.10", "chalk": "^4.1.2", "execa": "^5.0.0", "prompts": "^2.4.0"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "11.3.10", "@types/prompts": "^2.0.9"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-clean", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-clean"}, "gitHead": "59e4dac7e56fb05f33508ff804c0eac7448c16a8"}