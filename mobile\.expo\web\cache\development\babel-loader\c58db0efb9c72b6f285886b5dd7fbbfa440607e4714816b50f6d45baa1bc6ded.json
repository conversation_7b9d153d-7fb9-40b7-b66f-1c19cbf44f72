{"ast": null, "code": "'use strict';\nvar deepDiffer = function deepDiffer(one, two, maxDepth) {\n  if (maxDepth === void 0) {\n    maxDepth = -1;\n  }\n  if (maxDepth === 0) {\n    return true;\n  }\n  if (one === two) {\n    return false;\n  }\n  if (typeof one === 'function' && typeof two === 'function') {\n    return false;\n  }\n  if (typeof one !== 'object' || one === null) {\n    return one !== two;\n  }\n  if (typeof two !== 'object' || two === null) {\n    return true;\n  }\n  if (one.constructor !== two.constructor) {\n    return true;\n  }\n  if (Array.isArray(one)) {\n    var len = one.length;\n    if (two.length !== len) {\n      return true;\n    }\n    for (var ii = 0; ii < len; ii++) {\n      if (deepDiffer(one[ii], two[ii], maxDepth - 1)) {\n        return true;\n      }\n    }\n  } else {\n    for (var key in one) {\n      if (deepDiffer(one[key], two[key], maxDepth - 1)) {\n        return true;\n      }\n    }\n    for (var twoKey in two) {\n      if (one[twoKey] === undefined && two[twoKey] !== undefined) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nexport default deepDiffer;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "one", "two", "max<PERSON><PERSON><PERSON>", "constructor", "Array", "isArray", "len", "length", "ii", "key", "twoKey", "undefined"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/vendor/react-native/deepDiffer/index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\n/*\n * @returns {bool} true if different, false if equal\n */\nvar deepDiffer = function deepDiffer(one, two, maxDepth) {\n  if (maxDepth === void 0) {\n    maxDepth = -1;\n  }\n  if (maxDepth === 0) {\n    return true;\n  }\n  if (one === two) {\n    // Short circuit on identical object references instead of traversing them.\n    return false;\n  }\n  if (typeof one === 'function' && typeof two === 'function') {\n    // We consider all functions equal\n    return false;\n  }\n  if (typeof one !== 'object' || one === null) {\n    // Primitives can be directly compared\n    return one !== two;\n  }\n  if (typeof two !== 'object' || two === null) {\n    // We know they are different because the previous case would have triggered\n    // otherwise.\n    return true;\n  }\n  if (one.constructor !== two.constructor) {\n    return true;\n  }\n  if (Array.isArray(one)) {\n    // We know two is also an array because the constructors are equal\n    var len = one.length;\n    if (two.length !== len) {\n      return true;\n    }\n    for (var ii = 0; ii < len; ii++) {\n      if (deepDiffer(one[ii], two[ii], maxDepth - 1)) {\n        return true;\n      }\n    }\n  } else {\n    for (var key in one) {\n      if (deepDiffer(one[key], two[key], maxDepth - 1)) {\n        return true;\n      }\n    }\n    for (var twoKey in two) {\n      // The only case we haven't checked yet is keys that are in two but aren't\n      // in one, which means they are different.\n      if (one[twoKey] === undefined && two[twoKey] !== undefined) {\n        return true;\n      }\n    }\n  }\n  return false;\n};\nexport default deepDiffer;"], "mappings": "AAUA,YAAY;AAKZ,IAAIA,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAEC,GAAG,EAAEC,QAAQ,EAAE;EACvD,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;IACvBA,QAAQ,GAAG,CAAC,CAAC;EACf;EACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;IAClB,OAAO,IAAI;EACb;EACA,IAAIF,GAAG,KAAKC,GAAG,EAAE;IAEf,OAAO,KAAK;EACd;EACA,IAAI,OAAOD,GAAG,KAAK,UAAU,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;IAE1D,OAAO,KAAK;EACd;EACA,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAE3C,OAAOA,GAAG,KAAKC,GAAG;EACpB;EACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IAG3C,OAAO,IAAI;EACb;EACA,IAAID,GAAG,CAACG,WAAW,KAAKF,GAAG,CAACE,WAAW,EAAE;IACvC,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;IAEtB,IAAIM,GAAG,GAAGN,GAAG,CAACO,MAAM;IACpB,IAAIN,GAAG,CAACM,MAAM,KAAKD,GAAG,EAAE;MACtB,OAAO,IAAI;IACb;IACA,KAAK,IAAIE,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGF,GAAG,EAAEE,EAAE,EAAE,EAAE;MAC/B,IAAIT,UAAU,CAACC,GAAG,CAACQ,EAAE,CAAC,EAAEP,GAAG,CAACO,EAAE,CAAC,EAAEN,QAAQ,GAAG,CAAC,CAAC,EAAE;QAC9C,OAAO,IAAI;MACb;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAIO,GAAG,IAAIT,GAAG,EAAE;MACnB,IAAID,UAAU,CAACC,GAAG,CAACS,GAAG,CAAC,EAAER,GAAG,CAACQ,GAAG,CAAC,EAAEP,QAAQ,GAAG,CAAC,CAAC,EAAE;QAChD,OAAO,IAAI;MACb;IACF;IACA,KAAK,IAAIQ,MAAM,IAAIT,GAAG,EAAE;MAGtB,IAAID,GAAG,CAACU,MAAM,CAAC,KAAKC,SAAS,IAAIV,GAAG,CAACS,MAAM,CAAC,KAAKC,SAAS,EAAE;QAC1D,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd,CAAC;AACD,eAAeZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}