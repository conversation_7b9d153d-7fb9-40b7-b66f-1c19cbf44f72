{"name": "react-native-maps", "description": "React Native Mapview component for iOS + Android", "source": "src/index", "main": "lib/index.js", "author": "<PERSON><PERSON> <leland.m.rich<PERSON><EMAIL>>", "homepage": "https://github.com/react-native-maps/react-native-maps#readme", "version": "1.7.1", "license": "MIT", "scripts": {"lint": "eslint . --max-warnings 0", "tscheck": "tsc --noEmit", "format-check": "prettier --check .", "build": "tsc --project tsconfig.build.json", "test": "jest", "prepare": "husky install", "release": "semantic-release", "bundle-install": "cd example && bundle install", "pod-install": "cd example/ios && bundle exec pod install", "bootstrap": "yarn --cwd example && yarn && yarn bundle-install && yarn pod-install"}, "files": ["lib", "android", "ios", "react-native-google-maps.podspec", "react-native-maps.podspec", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "repository": {"type": "git", "url": "https://github.com/react-native-maps/react-native-maps"}, "keywords": ["react", "react-native", "react-component", "map", "mapview", "google-maps", "mapkit"], "peerDependencies": {"react": ">= 17.0.1", "react-native": ">= 0.64.3", "react-native-web": ">= 0.11"}, "peerDependenciesMeta": {"react-native-web": {"optional": true}}, "devDependencies": {"@commitlint/cli": "17.6.1", "@commitlint/config-conventional": "17.6.1", "@react-native-community/eslint-config": "3.2.0", "@semantic-release/changelog": "6.0.3", "@semantic-release/git": "10.0.1", "@types/jest": "29.5.1", "@types/react-native": "0.70.13", "babel-jest": "29.5.0", "detox": "20.7.0", "eslint": "8.38.0", "husky": "8.0.3", "jest": "29.5.0", "prettier": "2.8.7", "react": "18.1.0", "react-native": "0.70.9", "semantic-release": "21.0.1", "typescript": "5.0.4"}, "dependencies": {"@types/geojson": "^7946.0.10"}, "jest": {"preset": "react-native", "testPathIgnorePatterns": ["/node_modules/", "e2e"]}}