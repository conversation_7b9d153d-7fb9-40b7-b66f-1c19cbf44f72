{"ast": null, "code": "import _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"visible\", \"children\", \"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nvar FAR_FAR_AWAY = 30000;\nexport default function ResourceSavingScene(_ref) {\n  var visible = _ref.visible,\n    children = _ref.children,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  if (Platform.OS === 'web') {\n    return React.createElement(View, _extends({\n      hidden: !visible,\n      style: [{\n        display: visible ? 'flex' : 'none'\n      }, styles.container, style],\n      pointerEvents: visible ? 'auto' : 'none'\n    }, rest), children);\n  }\n  return React.createElement(View, {\n    style: [styles.container, style],\n    pointerEvents: visible ? 'auto' : 'none'\n  }, React.createElement(View, {\n    collapsable: false,\n    removeClippedSubviews: Platform.OS === 'ios' || Platform.OS === 'macos' ? !visible : true,\n    pointerEvents: visible ? 'auto' : 'none',\n    style: visible ? styles.attached : styles.detached\n  }, children));\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    overflow: 'hidden'\n  },\n  attached: {\n    flex: 1\n  },\n  detached: {\n    flex: 1,\n    top: FAR_FAR_AWAY\n  }\n});", "map": {"version": 3, "names": ["React", "Platform", "StyleSheet", "View", "FAR_FAR_AWAY", "ResourceSavingScene", "_ref", "visible", "children", "style", "rest", "_objectWithoutProperties", "_excluded", "OS", "createElement", "_extends", "hidden", "display", "styles", "container", "pointerEvents", "collapsable", "removeClippedSubviews", "attached", "detached", "create", "flex", "overflow", "top"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\elements\\src\\ResourceSavingView.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Platform, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';\n\ntype Props = {\n  visible: boolean;\n  children: React.ReactNode;\n  style?: StyleProp<ViewStyle>;\n};\n\nconst FAR_FAR_AWAY = 30000; // this should be big enough to move the whole view out of its container\n\nexport default function ResourceSavingScene({\n  visible,\n  children,\n  style,\n  ...rest\n}: Props) {\n  if (Platform.OS === 'web') {\n    return (\n      <View\n        // @ts-expect-error: hidden exists on web, but not in React Native\n        hidden={!visible}\n        style={[\n          { display: visible ? 'flex' : 'none' },\n          styles.container,\n          style,\n        ]}\n        pointerEvents={visible ? 'auto' : 'none'}\n        {...rest}\n      >\n        {children}\n      </View>\n    );\n  }\n\n  return (\n    <View\n      style={[styles.container, style]}\n      // box-none doesn't seem to work properly on Android\n      pointerEvents={visible ? 'auto' : 'none'}\n    >\n      <View\n        collapsable={false}\n        removeClippedSubviews={\n          // On iOS & macOS, set removeClippedSubviews to true only when not focused\n          // This is an workaround for a bug where the clipped view never re-appears\n          Platform.OS === 'ios' || Platform.OS === 'macos' ? !visible : true\n        }\n        pointerEvents={visible ? 'auto' : 'none'}\n        style={visible ? styles.attached : styles.detached}\n      >\n        {children}\n      </View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    overflow: 'hidden',\n  },\n  attached: {\n    flex: 1,\n  },\n  detached: {\n    flex: 1,\n    top: FAR_FAR_AWAY,\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAS9B,IAAMC,YAAY,GAAG,KAAK;AAE1B,eAAe,SAASC,mBAAmBA,CAAAC,IAAA,EAKjC;EAAA,IAJRC,OAAO,GAIDD,IAAA,CAJNC,OAAO;IACPC,QAAQ,GAGFF,IAAA,CAHNE,QAAQ;IACRC,KAAK,GAECH,IAAA,CAFNG,KAAK;IACFC,IAAA,GAAAC,wBAAA,CACGL,IAAA,EAAAM,SAAA;EACN,IAAIX,QAAQ,CAACY,EAAE,KAAK,KAAK,EAAE;IACzB,OACEb,KAAA,CAAAc,aAAA,CAACX,IAAA,EACCY,QAAA;MACAC,MAAM,EAAE,CAACT,OAAQ;MACjBE,KAAK,EAAE,CACL;QAAEQ,OAAO,EAAEV,OAAO,GAAG,MAAM,GAAG;MAAO,CAAC,EACtCW,MAAM,CAACC,SAAS,EAChBV,KAAK,CACL;MACFW,aAAa,EAAEb,OAAO,GAAG,MAAM,GAAG;IAAO,GACrCG,IAAI,GAEPF,QAAQ,CACJ;EAEX;EAEA,OACER,KAAA,CAAAc,aAAA,CAACX,IAAI;IACHM,KAAK,EAAE,CAACS,MAAM,CAACC,SAAS,EAAEV,KAAK;IAE/BW,aAAa,EAAEb,OAAO,GAAG,MAAM,GAAG;EAAO,GAEzCP,KAAA,CAAAc,aAAA,CAACX,IAAI;IACHkB,WAAW,EAAE,KAAM;IACnBC,qBAAqB,EAGnBrB,QAAQ,CAACY,EAAE,KAAK,KAAK,IAAIZ,QAAQ,CAACY,EAAE,KAAK,OAAO,GAAG,CAACN,OAAO,GAAG,IAC/D;IACDa,aAAa,EAAEb,OAAO,GAAG,MAAM,GAAG,MAAO;IACzCE,KAAK,EAAEF,OAAO,GAAGW,MAAM,CAACK,QAAQ,GAAGL,MAAM,CAACM;EAAS,GAElDhB,QAAQ,CACJ,CACF;AAEX;AAEA,IAAMU,MAAM,GAAGhB,UAAU,CAACuB,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC;EACDJ,QAAQ,EAAE;IACRG,IAAI,EAAE;EACR,CAAC;EACDF,QAAQ,EAAE;IACRE,IAAI,EAAE,CAAC;IACPE,GAAG,EAAExB;EACP;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}