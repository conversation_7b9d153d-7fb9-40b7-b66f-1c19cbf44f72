{"ast": null, "code": "export default function pick(obj, list) {\n  var nextObj = {};\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      if (list[key] === true) {\n        nextObj[key] = obj[key];\n      }\n    }\n  }\n  return nextObj;\n}", "map": {"version": 3, "names": ["pick", "obj", "list", "nextObj", "key", "hasOwnProperty"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/modules/pick/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nexport default function pick(obj, list) {\n  var nextObj = {};\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      if (list[key] === true) {\n        nextObj[key] = obj[key];\n      }\n    }\n  }\n  return nextObj;\n}"], "mappings": "AASA,eAAe,SAASA,IAAIA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACtC,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,KAAK,IAAIC,GAAG,IAAIH,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;MAC3B,IAAIF,IAAI,CAACE,GAAG,CAAC,KAAK,IAAI,EAAE;QACtBD,OAAO,CAACC,GAAG,CAAC,GAAGH,GAAG,CAACG,GAAG,CAAC;MACzB;IACF;EACF;EACA,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}