# Frontend UI/UX Guidelines

Here are the frontend guideline rules based on the provided design. These guidelines aim to ensure consistency, scalability, and a high-quality user experience for the application.

---

## 🎨 Color Palette

The color scheme is clean and functional, using a primary action color with neutral tones for content. Brand colors are used contextually for map pins and branding.

| Color               | Hex (Estimated) | Usage                                                                      |
| ------------------- | --------------- | -------------------------------------------------------------------------- |
| **Primary Blue** | `#0D6EFD`       | Primary CTAs (`Obtener ruta`), active graph elements, user location icon.    |
| **Primary Dark** | `#0B2B40`       | Secondary CTAs (`Evaluar Gasolinera`), emphasized text.                      |
| **Text Primary** | `#212529`       | Headings, primary text content, current price.                             |
| **Text Secondary** | `#6c757d`       | Subheadings, labels, secondary information (distance, time, ratings text). |
| **Success Green** | `#198754`       | High availability status.                                                  |
| **Background** | `#FFFFFF`       | Main background for sheets and cards.                                      |
| **Border / Divider**| `#DEE2E6`       | Borders for inputs, dividers, and graph grid lines.                        |
| **Neutral Gray** | `#F8F9FA`       | Map background, disabled states.                                           |

---

## Typography

A clear and legible typography scale is used to create a strong visual hierarchy. Use a sans-serif font like **Inter** or **Roboto**.

* **Header 1 (Location Name)**
    * Font Size: `24px`
    * Font Weight: **Bold** (`700`)
    * Color: `Text Primary`
    * Example: "Gasolinera Shell"

* **Header 2 (Price)**
    * Font Size: `28px`
    * Font Weight: **Bold** (`700`)
    * Color: `Primary Blue`
    * Example: "$25.99"

* **Subheading (Section Title)**
    * Font Size: `14px`
    * Font Weight: **Medium** (`500`)
    * Color: `Text Secondary`
    * Example: "Disponibilidad", "Precio Actual"

* **Body**
    * Font Size: `16px`
    * Font Weight: **Regular** (`400`)
    * Color: `Text Primary`
    * Usage: General text, dropdown selected value.

* **Caption**
    * Font Size: `14px`
    * Font Weight: **Regular** (`400`)
    * Color: `Text Secondary`
    * Usage: Metadata like distance, time, ratings count, and graph axis labels.

---

## 📐 Layout & Spacing

A consistent spacing system based on an **8px grid** should be used for all padding, margins, and component layouts.

* **Base Unit:** `1 unit = 8px`
* **Sheet Padding:** `3 units` (`24px`) for horizontal and vertical padding inside the main content sheet.
* **Component Spacing:** `2 units` (`16px`) margin between major elements (e.g., between the image and the price section).
* **Inline Spacing:** `1 unit` (`8px`) between related inline items (e.g., between the star rating and the review count).
* **Map UI Spacing:** `2 units` (`16px`) spacing from the screen edges for floating map controls (filter button, profile icon).

---

## 🧩 Component Library

### Buttons

Buttons are a primary way for users to take action. They should have clear states for default, hover, and pressed.

* **Primary CTA (Call to Action)**
    * **Style:** Filled background.
    * **Background Color:** `Primary Blue`
    * **Text Color:** `Background` (White)
    * **Border Radius:** `24px` (Pill shape)
    * **Icon:** Use a solid, filled icon placed before the text.
    * **Example:** `Obtener ruta` (Get Route)

* **Secondary CTA**
    * **Style:** Filled background.
    * **Background Color:** `Primary Dark`
    * **Text Color:** `Background` (White)
    * **Border Radius:** `24px` (Pill shape)
    * **Example:** `Evaluar Gasolinera` (Evaluate Gas Station)

### Cards & Sheets

The primary content is displayed in a bottom sheet that floats above the map.

* **Background Color:** `Background` (White)
* **Border Radius:** `16px` for top-left and top-right corners only. The bottom corners should be sharp.
* **Shadow:** A soft shadow should be applied to create elevation and separate the sheet from the map.
    * `box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);`

### Form Elements

* **Dropdown Selector**
    * **Background:** `Background` (White)
    * **Border:** `1px solid` using `Border / Divider` color.
    * **Border Radius:** `8px`
    * **Padding:** `12px 16px`
    * **Icon:** A standard chevron-down icon on the right.

### Graph

The price history graph is a key data visualization component.

* **Type:** Line chart with a subtle gradient area fill underneath.
* **Line Color:** `Primary Blue`
* **Grid Lines:** Dashed, using `Border / Divider` color.
* **Data Point Highlight:** When a user interacts with a point on the graph (e.g., `lun, 5 may`), display a circular marker on the line and a tooltip/label showing the exact value. The label should have a dark background and white text.