{"ast": null, "code": "export default function findFocusedRoute(state) {\n  var _ref;\n  var _current2, _current3;\n  var current = state;\n  while (((_current = current) === null || _current === void 0 ? void 0 : _current.routes[(_current$index = current.index) != null ? _current$index : 0].state) != null) {\n    var _current$index, _current$index2;\n    var _current;\n    current = current.routes[(_current$index2 = current.index) != null ? _current$index2 : 0].state;\n  }\n  var route = (_current2 = current) === null || _current2 === void 0 ? void 0 : _current2.routes[(_ref = (_current3 = current) === null || _current3 === void 0 ? void 0 : _current3.index) != null ? _ref : 0];\n  return route;\n}", "map": {"version": 3, "names": ["findFocusedRoute", "state", "_ref", "_current2", "_current3", "current", "_current", "routes", "_current$index", "index", "_current$index2", "route"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\findFocusedRoute.tsx"], "sourcesContent": ["import type { InitialState } from '@react-navigation/routers';\n\nexport default function findFocusedRoute(state: InitialState) {\n  let current: InitialState | undefined = state;\n\n  while (current?.routes[current.index ?? 0].state != null) {\n    current = current.routes[current.index ?? 0].state;\n  }\n\n  const route = current?.routes[current?.index ?? 0];\n\n  return route;\n}\n"], "mappings": "AAEA,eAAe,SAASA,gBAAgBA,CAACC,KAAmB,EAAE;EAAA,IAAAC,IAAA;EAAA,IAAAC,SAAA,EAAAC,SAAA;EAC5D,IAAIC,OAAiC,GAAGJ,KAAK;EAE7C,OAAO,EAAAK,QAAA,GAAAD,OAAO,cAAAC,QAAA,uBAAPA,QAAA,CAASC,MAAM,EAAAC,cAAA,GAACH,OAAO,CAACI,KAAK,YAAAD,cAAA,GAAI,CAAC,CAAC,CAACP,KAAK,KAAI,IAAI,EAAE;IAAA,IAAAO,cAAA,EAAAE,eAAA;IAAA,IAAAJ,QAAA;IACxDD,OAAO,GAAGA,OAAO,CAACE,MAAM,EAAAG,eAAA,GAACL,OAAO,CAACI,KAAK,YAAAC,eAAA,GAAI,CAAC,CAAC,CAACT,KAAK;EACpD;EAEA,IAAMU,KAAK,IAAAR,SAAA,GAAGE,OAAO,cAAAF,SAAA,uBAAPA,SAAA,CAASI,MAAM,EAAAL,IAAA,GAAC,CAAAE,SAAA,GAAAC,OAAO,cAAAD,SAAA,uBAAPA,SAAA,CAASK,KAAK,YAAAP,IAAA,GAAI,CAAC,CAAC;EAElD,OAAOS,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}