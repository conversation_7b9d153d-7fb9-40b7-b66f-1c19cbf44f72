{"ast": null, "code": "'use client';\n\nimport AccessibilityUtil from \"../../modules/AccessibilityUtil\";\nimport createDOMProps from \"../../modules/createDOMProps\";\nimport React from 'react';\nimport { LocaleProvider } from \"../../modules/useLocale\";\nvar createElement = function createElement(component, props, options) {\n  var accessibilityComponent;\n  if (component && component.constructor === String) {\n    accessibilityComponent = AccessibilityUtil.propsToAccessibilityComponent(props);\n  }\n  var Component = accessibilityComponent || component;\n  var domProps = createDOMProps(Component, props, options);\n  var element = React.createElement(Component, domProps);\n  var elementWithLocaleProvider = domProps.dir ? React.createElement(LocaleProvider, {\n    children: element,\n    direction: domProps.dir,\n    locale: domProps.lang\n  }) : element;\n  return elementWithLocaleProvider;\n};\nexport default createElement;", "map": {"version": 3, "names": ["AccessibilityUtil", "createDOMProps", "React", "LocaleProvider", "createElement", "component", "props", "options", "accessibilityComponent", "constructor", "String", "propsToAccessibilityComponent", "Component", "domProps", "element", "elementWithLocaleProvider", "dir", "children", "direction", "locale", "lang"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/exports/createElement/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nimport AccessibilityUtil from '../../modules/AccessibilityUtil';\nimport createDOMProps from '../../modules/createDOMProps';\nimport React from 'react';\nimport { LocaleProvider } from '../../modules/useLocale';\nvar createElement = (component, props, options) => {\n  // Use equivalent platform elements where possible.\n  var accessibilityComponent;\n  if (component && component.constructor === String) {\n    accessibilityComponent = AccessibilityUtil.propsToAccessibilityComponent(props);\n  }\n  var Component = accessibilityComponent || component;\n  var domProps = createDOMProps(Component, props, options);\n  var element = /*#__PURE__*/React.createElement(Component, domProps);\n\n  // Update locale context if element's writing direction prop changes\n  var elementWithLocaleProvider = domProps.dir ? /*#__PURE__*/React.createElement(LocaleProvider, {\n    children: element,\n    direction: domProps.dir,\n    locale: domProps.lang\n  }) : element;\n  return elementWithLocaleProvider;\n};\nexport default createElement;"], "mappings": "AASA,YAAY;;AAEZ,OAAOA,iBAAiB;AACxB,OAAOC,cAAc;AACrB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc;AACvB,IAAIC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,SAAS,EAAEC,KAAK,EAAEC,OAAO,EAAK;EAEjD,IAAIC,sBAAsB;EAC1B,IAAIH,SAAS,IAAIA,SAAS,CAACI,WAAW,KAAKC,MAAM,EAAE;IACjDF,sBAAsB,GAAGR,iBAAiB,CAACW,6BAA6B,CAACL,KAAK,CAAC;EACjF;EACA,IAAIM,SAAS,GAAGJ,sBAAsB,IAAIH,SAAS;EACnD,IAAIQ,QAAQ,GAAGZ,cAAc,CAACW,SAAS,EAAEN,KAAK,EAAEC,OAAO,CAAC;EACxD,IAAIO,OAAO,GAAgBZ,KAAK,CAACE,aAAa,CAACQ,SAAS,EAAEC,QAAQ,CAAC;EAGnE,IAAIE,yBAAyB,GAAGF,QAAQ,CAACG,GAAG,GAAgBd,KAAK,CAACE,aAAa,CAACD,cAAc,EAAE;IAC9Fc,QAAQ,EAAEH,OAAO;IACjBI,SAAS,EAAEL,QAAQ,CAACG,GAAG;IACvBG,MAAM,EAAEN,QAAQ,CAACO;EACnB,CAAC,CAAC,GAAGN,OAAO;EACZ,OAAOC,yBAAyB;AAClC,CAAC;AACD,eAAeX,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}