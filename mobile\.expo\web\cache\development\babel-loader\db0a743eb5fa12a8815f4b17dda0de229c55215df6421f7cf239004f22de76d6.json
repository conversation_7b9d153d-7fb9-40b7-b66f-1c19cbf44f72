{"ast": null, "code": "export default function MaskedView(_ref) {\n  var children = _ref.children;\n  return children;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "children"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\elements\\src\\MaskedView.tsx"], "sourcesContent": ["/**\n * Use a stub for MaskedView on all Platforms that don't support it.\n */\nimport type * as React from 'react';\n\ntype Props = {\n  maskElement: React.ReactElement;\n  children: React.ReactElement;\n};\n\nexport default function MaskedView({ children }: Props) {\n  return children;\n}\n"], "mappings": "AAUA,eAAe,SAASA,UAAUA,CAAAC,IAAA,EAAsB;EAAA,IAAnBC,QAAA,GAAiBD,IAAA,CAAjBC,QAAA;EACnC,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}