{"version": 3, "file": "Location.d.ts", "sourceRoot": "", "sources": ["../src/Location.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,gBAAgB,EAChB,kBAAkB,EAClB,qBAAqB,EAGtB,MAAM,mBAAmB,CAAC;AAG3B,OAAO,EACL,gBAAgB,EAChB,gBAAgB,EAChB,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,qBAAqB,EACrB,wBAAwB,EACxB,cAAc,EACd,eAAe,EACf,0BAA0B,EAC1B,sBAAsB,EACtB,cAAc,EACd,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,2BAA2B,EAC3B,6BAA6B,EAC7B,wBAAwB,EACzB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAyC,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAGlG;;;;GAIG;AACH,iBAAS,eAAe,CAAC,OAAO,EAAE,MAAM,QAAI;AAG5C;;;GAGG;AACH,wBAAsB,sBAAsB,IAAI,OAAO,CAAC,sBAAsB,CAAC,CAE9E;AAGD;;;;GAIG;AACH,wBAAsB,0BAA0B,IAAI,OAAO,CAAC,IAAI,CAAC,CAShE;AAGD;;;;;;;;;GASG;AACH,wBAAsB,uBAAuB,CAC3C,OAAO,GAAE,eAAoB,GAC5B,OAAO,CAAC,cAAc,CAAC,CAEzB;AAGD;;;;;;;;;GASG;AACH,wBAAsB,yBAAyB,CAC7C,OAAO,GAAE,wBAA6B,GACrC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,CAEhC;AAGD;;;;;;;;GAQG;AACH,wBAAsB,kBAAkB,CACtC,OAAO,EAAE,eAAe,EACxB,QAAQ,EAAE,gBAAgB,GACzB,OAAO,CAAC,oBAAoB,CAAC,CAS/B;AAGD;;;;GAIG;AACH,wBAAsB,eAAe,IAAI,OAAO,CAAC,qBAAqB,CAAC,CAatE;AAGD;;;;;GAKG;AACH,wBAAsB,iBAAiB,CACrC,QAAQ,EAAE,uBAAuB,GAChC,OAAO,CAAC,oBAAoB,CAAC,CAS/B;AAGD;;;;;;;;;;;;;;GAcG;AACH,wBAAsB,YAAY,CAChC,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,wBAAwB,GACjC,OAAO,CAAC,wBAAwB,EAAE,CAAC,CAgBrC;AAGD;;;;;;;;;;;;;;GAcG;AACH,wBAAsB,mBAAmB,CACvC,QAAQ,EAAE,IAAI,CAAC,wBAAwB,EAAE,UAAU,GAAG,WAAW,CAAC,EAClE,OAAO,CAAC,EAAE,wBAAwB,GACjC,OAAO,CAAC,uBAAuB,EAAE,CAAC,CAkBpC;AAGD;;;;GAIG;AACH,wBAAsB,mBAAmB,IAAI,OAAO,CAAC,0BAA0B,CAAC,CAK/E;AAGD;;;;GAIG;AACH,wBAAsB,uBAAuB,IAAI,OAAO,CAAC,0BAA0B,CAAC,CAMnF;AAGD;;;GAGG;AACH,wBAAsB,6BAA6B,IAAI,OAAO,CAAC,0BAA0B,CAAC,CAEzF;AAGD;;;GAGG;AACH,wBAAsB,iCAAiC,IAAI,OAAO,CAAC,0BAA0B,CAAC,CAE7F;AAGD;;;;;;;;GAQG;AACH,eAAO,MAAM,wBAAwB,oLAGnC,CAAC;AAGH;;;GAGG;AACH,wBAAsB,6BAA6B,IAAI,OAAO,CAAC,kBAAkB,CAAC,CAEjF;AAGD;;;;;;;;GAQG;AACH,wBAAsB,iCAAiC,IAAI,OAAO,CAAC,kBAAkB,CAAC,CAErF;AAGD;;;;;;;;;GASG;AACH,eAAO,MAAM,wBAAwB,4JAGnC,CAAC;AAKH;;;;GAIG;AACH,wBAAsB,uBAAuB,IAAI,OAAO,CAAC,OAAO,CAAC,CAEhE;AAWD,wBAAsB,kCAAkC,IAAI,OAAO,CAAC,OAAO,CAAC,CAG3E;AAGD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAsB,yBAAyB,CAC7C,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,mBAA6D,GACrE,OAAO,CAAC,IAAI,CAAC,CAGf;AAGD;;;;GAIG;AACH,wBAAsB,wBAAwB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAG9E;AAGD;;;;GAIG;AACH,wBAAsB,8BAA8B,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAGvF;AA0BD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,wBAAsB,oBAAoB,CACxC,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,cAAc,EAAO,GAC7B,OAAO,CAAC,IAAI,CAAC,CAIf;AAGD;;;;;GAKG;AACH,wBAAsB,mBAAmB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAGzE;AAGD;;;;GAIG;AACH,wBAAsB,yBAAyB,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAGlF;AAED,OAAO,EAAE,oBAAoB,IAAI,YAAY,EAAE,kBAAkB,EAAE,CAAC;AAEpE,OAAO,EACL,gBAAgB,IAAI,QAAQ,EAC5B,oBAAoB,IAAI,YAAY,EACpC,2BAA2B,IAAI,mBAAmB,EAClD,6BAA6B,IAAI,qBAAqB,EACtD,gBAAgB,EAChB,qBAAqB,EACrB,eAAe,GAChB,CAAC;AAEF,OAAO,EAAE,6BAA6B,EAAE,MAAM,uBAAuB,CAAC;AACtE,cAAc,kBAAkB,CAAC"}