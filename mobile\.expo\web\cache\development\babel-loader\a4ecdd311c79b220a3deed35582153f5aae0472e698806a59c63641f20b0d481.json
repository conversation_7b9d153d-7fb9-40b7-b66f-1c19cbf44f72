{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport _class<PERSON>allCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nvar EventEmitter = function () {\n  function EventEmitter() {\n    _classCallCheck(this, EventEmitter);\n    this._registry = {};\n  }\n  return _createClass(EventEmitter, [{\n    key: \"addListener\",\n    value: function addListener(eventType, listener, context) {\n      var registrations = allocate(this._registry, eventType);\n      var registration = {\n        context: context,\n        listener: listener,\n        remove: function remove() {\n          registrations.delete(registration);\n        }\n      };\n      registrations.add(registration);\n      return registration;\n    }\n  }, {\n    key: \"emit\",\n    value: function emit(eventType) {\n      var registrations = this._registry[eventType];\n      if (registrations != null) {\n        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n          args[_key - 1] = arguments[_key];\n        }\n        for (var _i = 0, _arr = _toConsumableArray(registrations); _i < _arr.length; _i++) {\n          var registration = _arr[_i];\n          registration.listener.apply(registration.context, args);\n        }\n      }\n    }\n  }, {\n    key: \"removeAllListeners\",\n    value: function removeAllListeners(eventType) {\n      if (eventType == null) {\n        this._registry = {};\n      } else {\n        delete this._registry[eventType];\n      }\n    }\n  }, {\n    key: \"listenerCount\",\n    value: function listenerCount(eventType) {\n      var registrations = this._registry[eventType];\n      return registrations == null ? 0 : registrations.size;\n    }\n  }]);\n}();\nexport { EventEmitter as default };\nfunction allocate(registry, eventType) {\n  var registrations = registry[eventType];\n  if (registrations == null) {\n    registrations = new Set();\n    registry[eventType] = registrations;\n  }\n  return registrations;\n}", "map": {"version": 3, "names": ["EventEmitter", "_classCallCheck", "_registry", "_createClass", "key", "value", "addListener", "eventType", "listener", "context", "registrations", "allocate", "registration", "remove", "delete", "add", "emit", "_len", "arguments", "length", "args", "Array", "_key", "_i", "_arr", "_toConsumableArray", "apply", "removeAllListeners", "listenerCount", "size", "default", "registry", "Set"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/vendor/react-native/vendor/emitter/EventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * EventEmitter manages listeners and publishes events to them.\n *\n * EventEmitter accepts a single type parameter that defines the valid events\n * and associated listener argument(s).\n *\n * @example\n *\n *   const emitter = new EventEmitter<{\n *     success: [number, string],\n *     error: [Error],\n *   }>();\n *\n *   emitter.on('success', (statusCode, responseText) => {...});\n *   emitter.emit('success', 200, '...');\n *\n *   emitter.on('error', error => {...});\n *   emitter.emit('error', new Error('Resource not found'));\n *\n */\nexport default class EventEmitter {\n  constructor() {\n    this._registry = {};\n  }\n  /**\n   * Registers a listener that is called when the supplied event is emitted.\n   * Returns a subscription that has a `remove` method to undo registration.\n   */\n  addListener(eventType, listener, context) {\n    var registrations = allocate(this._registry, eventType);\n    var registration = {\n      context,\n      listener,\n      remove() {\n        registrations.delete(registration);\n      }\n    };\n    registrations.add(registration);\n    return registration;\n  }\n\n  /**\n   * Emits the supplied event. Additional arguments supplied to `emit` will be\n   * passed through to each of the registered listeners.\n   *\n   * If a listener modifies the listeners registered for the same event, those\n   * changes will not be reflected in the current invocation of `emit`.\n   */\n  emit(eventType) {\n    var registrations = this._registry[eventType];\n    if (registrations != null) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      for (var _i = 0, _arr = [...registrations]; _i < _arr.length; _i++) {\n        var registration = _arr[_i];\n        registration.listener.apply(registration.context, args);\n      }\n    }\n  }\n\n  /**\n   * Removes all registered listeners.\n   */\n  removeAllListeners(eventType) {\n    if (eventType == null) {\n      this._registry = {};\n    } else {\n      delete this._registry[eventType];\n    }\n  }\n\n  /**\n   * Returns the number of registered listeners for the supplied event.\n   */\n  listenerCount(eventType) {\n    var registrations = this._registry[eventType];\n    return registrations == null ? 0 : registrations.size;\n  }\n}\nfunction allocate(registry, eventType) {\n  var registrations = registry[eventType];\n  if (registrations == null) {\n    registrations = new Set();\n    registry[eventType] = registrations;\n  }\n  return registrations;\n}"], "mappings": ";;;IA8BqBA,YAAY;EAC/B,SAAAA,aAAA,EAAc;IAAAC,eAAA,OAAAD,YAAA;IACZ,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC;EACrB;EAAC,OAAAC,YAAA,CAAAH,YAAA;IAAAI,GAAA;IAAAC,KAAA,EAKD,SAAAC,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MACxC,IAAIC,aAAa,GAAGC,QAAQ,CAAC,IAAI,CAACT,SAAS,EAAEK,SAAS,CAAC;MACvD,IAAIK,YAAY,GAAG;QACjBH,OAAO,EAAPA,OAAO;QACPD,QAAQ,EAARA,QAAQ;QACRK,MAAM,WAANA,MAAMA,CAAA,EAAG;UACPH,aAAa,CAACI,MAAM,CAACF,YAAY,CAAC;QACpC;MACF,CAAC;MACDF,aAAa,CAACK,GAAG,CAACH,YAAY,CAAC;MAC/B,OAAOA,YAAY;IACrB;EAAC;IAAAR,GAAA;IAAAC,KAAA,EASD,SAAAW,IAAIA,CAACT,SAAS,EAAE;MACd,IAAIG,aAAa,GAAG,IAAI,CAACR,SAAS,CAACK,SAAS,CAAC;MAC7C,IAAIG,aAAa,IAAI,IAAI,EAAE;QACzB,KAAK,IAAIO,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QAClC;QACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,IAAI,GAAAC,kBAAA,CAAOf,aAAa,CAAC,EAAEa,EAAE,GAAGC,IAAI,CAACL,MAAM,EAAEI,EAAE,EAAE,EAAE;UAClE,IAAIX,YAAY,GAAGY,IAAI,CAACD,EAAE,CAAC;UAC3BX,YAAY,CAACJ,QAAQ,CAACkB,KAAK,CAACd,YAAY,CAACH,OAAO,EAAEW,IAAI,CAAC;QACzD;MACF;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAAsB,kBAAkBA,CAACpB,SAAS,EAAE;MAC5B,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI,CAACL,SAAS,GAAG,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,OAAO,IAAI,CAACA,SAAS,CAACK,SAAS,CAAC;MAClC;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAKD,SAAAuB,aAAaA,CAACrB,SAAS,EAAE;MACvB,IAAIG,aAAa,GAAG,IAAI,CAACR,SAAS,CAACK,SAAS,CAAC;MAC7C,OAAOG,aAAa,IAAI,IAAI,GAAG,CAAC,GAAGA,aAAa,CAACmB,IAAI;IACvD;EAAC;AAAA;AAAA,SA1DkB7B,YAAY,IAAA8B,OAAA;AA4DjC,SAASnB,QAAQA,CAACoB,QAAQ,EAAExB,SAAS,EAAE;EACrC,IAAIG,aAAa,GAAGqB,QAAQ,CAACxB,SAAS,CAAC;EACvC,IAAIG,aAAa,IAAI,IAAI,EAAE;IACzBA,aAAa,GAAG,IAAIsB,GAAG,CAAC,CAAC;IACzBD,QAAQ,CAACxB,SAAS,CAAC,GAAGG,aAAa;EACrC;EACA,OAAOA,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}