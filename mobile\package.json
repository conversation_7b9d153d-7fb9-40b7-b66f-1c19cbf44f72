{"name": "gaslink-mobile", "version": "1.0.0", "description": "GasLink Mobile App - Find and review gas stations", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject"}, "dependencies": {"@expo/webpack-config": "^19.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.13", "expo": "~49.0.15", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.72.6", "react-native-maps": "^1.26.14", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.3.0", "react-native-web": "~0.19.6"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}