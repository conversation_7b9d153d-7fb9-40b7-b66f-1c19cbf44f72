{"version": 3, "file": "ExpoLocation.web.js", "sourceRoot": "", "sources": ["../src/ExpoLocation.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAsB,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AAEzE,OAAO,EAIL,gBAAgB,GACjB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAE9D,MAAM,aAAc,SAAQ,KAAK;IAC/B,IAAI,CAAS;IAEb;QACE,KAAK,CAAC,oDAAoD,CAAC,CAAC;QAC5D,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AAED;;GAEG;AACH,SAAS,yBAAyB,CAAC,QAAwB;IACzD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,QAAQ,CAAC;IACvC,OAAO;QACL,MAAM,EAAE;YACN,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YACzC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,KAAK,EAAE,MAAM,CAAC,KAAK;SACpB;QACD,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,QAAwB,EAAE,OAAiC;IAClF,MAAM,MAAM,GAAG,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC9E,MAAM,gBAAgB,GACpB,OAAO,OAAO,CAAC,gBAAgB,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,QAAQ,CAAC;IACrF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,CAAC;IAE9D,OAAO,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,SAAS,IAAI,MAAM,IAAI,gBAAgB,IAAI,gBAAgB,CAAC;AAC3F,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,mBAAmB;IAChC,OAAO,IAAI,OAAO,CAAqB,CAAC,OAAO,EAAE,EAAE;QACjD,MAAM,iBAAiB,GAAG,CAAC,MAAM,EAAE,EAAE,CACnC,OAAO,CAAC;YACN,MAAM;YACN,OAAO,EAAE,MAAM,KAAK,gBAAgB,CAAC,OAAO;YAC5C,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,CAAC;SACX,CAAC,CAAC;QAEL,SAAS,CAAC,WAAW,CAAC,kBAAkB,CACtC,GAAG,EAAE,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,EACjD,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;YACX,IAAI,IAAI,KAAK,CAAC,CAAC,uBAAuB,EAAE;gBACtC,iBAAiB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;aAC5C;iBAAM;gBACL,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;aAClD;QACH,CAAC,EACD,EAAE,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,CACpD,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,iBAAiB,GAA0B,IAAI,CAAC;AAEpD,eAAe;IACb,IAAI,IAAI;QACN,OAAO,cAAc,CAAC;IACxB,CAAC;IACD,KAAK,CAAC,sBAAsB;QAC1B,OAAO;YACL,uBAAuB,EAAE,aAAa,IAAI,SAAS;SACpD,CAAC;IACJ,CAAC;IACD,KAAK,CAAC,yBAAyB,CAC7B,UAAoC,EAAE;QAEtC,IAAI,iBAAiB,IAAI,eAAe,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE;YACpE,OAAO,iBAAiB,CAAC;SAC1B;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,KAAK,CAAC,uBAAuB,CAAC,OAAwB;QACpD,OAAO,IAAI,OAAO,CAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrD,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC5B,iBAAiB,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;gBACxD,OAAO,CAAC,iBAAiB,CAAC,CAAC;YAC7B,CAAC,CAAC;YACF,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,QAAQ,EAAE,MAAM,EAAE;gBACzD,UAAU,EAAE,QAAQ;gBACpB,kBAAkB,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,gBAAgB,CAAC,QAAQ;gBACvE,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IACD,KAAK,CAAC,gBAAgB,CAAC,OAAO;QAC5B,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC;IACD,KAAK,CAAC,kBAAkB,CAAC,SAAS;QAChC,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;IACvE,CAAC;IACD,KAAK,CAAC,uBAAuB;QAC3B,OAAO,aAAa,IAAI,SAAS,CAAC;IACpC,CAAC;IACD,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,aAAa,EAAE,CAAC;IAC5B,CAAC;IACD,KAAK,CAAC,mBAAmB;QACvB,MAAM,IAAI,aAAa,EAAE,CAAC;IAC5B,CAAC;IACD,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,OAAwB;QACpE,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,EAAE;YACrC,8CAA8C;YAC9C,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAClD,CAAC,QAAQ,EAAE,EAAE;gBACX,iBAAiB,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;gBACxD,oBAAoB,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBAChD,OAAO;oBACP,QAAQ,EAAE,iBAAiB;iBAC5B,CAAC,CAAC;YACL,CAAC,EACD,SAAS;YACT,mDAAmD;YACnD,OAAO,CACR,CAAC;YACF,OAAO,CAAC,OAAO,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB;IACnB,KAAK,CAAC,uBAAuB;QAC3B,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,KAAK,CAAC,iCAAiC;QACrC,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,KAAK,CAAC,iCAAiC;QACrC,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IACD,KAAK,CAAC,6BAA6B;QACjC,OAAO,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED,QAAQ;IACR,cAAc,KAAI,CAAC;IACnB,aAAa,KAAI,CAAC;CACnB,CAAC", "sourcesContent": ["import { PermissionResponse, PermissionStatus } from 'expo-modules-core';\n\nimport {\n  LocationLastKnownOptions,\n  LocationObject,\n  LocationOptions,\n  LocationAccuracy,\n} from './Location.types';\nimport { LocationEventEmitter } from './LocationEventEmitter';\n\nclass GeocoderError extends Error {\n  code: string;\n\n  constructor() {\n    super('Geocoder service is not available for this device.');\n    this.code = 'E_NO_GEOCODER';\n  }\n}\n\n/**\n * Converts `GeolocationPosition` to JavaScript object.\n */\nfunction geolocationPositionToJSON(position: LocationObject): LocationObject {\n  const { coords, timestamp } = position;\n  return {\n    coords: {\n      latitude: coords.latitude,\n      longitude: coords.longitude,\n      altitude: coords.altitude,\n      accuracy: coords.accuracy,\n      altitudeAccuracy: coords.altitudeAccuracy,\n      heading: coords.heading,\n      speed: coords.speed,\n    },\n    timestamp,\n  };\n}\n\n/**\n * Checks whether given location didn't exceed given `maxAge` and fits in the required accuracy.\n */\nfunction isLocationValid(location: LocationObject, options: LocationLastKnownOptions): boolean {\n  const maxAge = typeof options.maxAge === 'number' ? options.maxAge : Infinity;\n  const requiredAccuracy =\n    typeof options.requiredAccuracy === 'number' ? options.requiredAccuracy : Infinity;\n  const locationAccuracy = location.coords.accuracy ?? Infinity;\n\n  return Date.now() - location.timestamp <= maxAge && locationAccuracy <= requiredAccuracy;\n}\n\n/**\n * Gets the permission details. The implementation is not very good as it actually requests\n * for the current location, but there is no better way on web so far :(\n */\nasync function getPermissionsAsync(): Promise<PermissionResponse> {\n  return new Promise<PermissionResponse>((resolve) => {\n    const resolveWithStatus = (status) =>\n      resolve({\n        status,\n        granted: status === PermissionStatus.GRANTED,\n        canAskAgain: true,\n        expires: 0,\n      });\n\n    navigator.geolocation.getCurrentPosition(\n      () => resolveWithStatus(PermissionStatus.GRANTED),\n      ({ code }) => {\n        if (code === 1 /* PERMISSION_DENIED */) {\n          resolveWithStatus(PermissionStatus.DENIED);\n        } else {\n          resolveWithStatus(PermissionStatus.UNDETERMINED);\n        }\n      },\n      { enableHighAccuracy: false, maximumAge: Infinity }\n    );\n  });\n}\n\nlet lastKnownPosition: LocationObject | null = null;\n\nexport default {\n  get name(): string {\n    return 'ExpoLocation';\n  },\n  async getProviderStatusAsync(): Promise<{ locationServicesEnabled: boolean }> {\n    return {\n      locationServicesEnabled: 'geolocation' in navigator,\n    };\n  },\n  async getLastKnownPositionAsync(\n    options: LocationLastKnownOptions = {}\n  ): Promise<LocationObject | null> {\n    if (lastKnownPosition && isLocationValid(lastKnownPosition, options)) {\n      return lastKnownPosition;\n    }\n    return null;\n  },\n  async getCurrentPositionAsync(options: LocationOptions): Promise<LocationObject> {\n    return new Promise<LocationObject>((resolve, reject) => {\n      const resolver = (position) => {\n        lastKnownPosition = geolocationPositionToJSON(position);\n        resolve(lastKnownPosition);\n      };\n      navigator.geolocation.getCurrentPosition(resolver, reject, {\n        maximumAge: Infinity,\n        enableHighAccuracy: (options.accuracy ?? 0) > LocationAccuracy.Balanced,\n        ...options,\n      });\n    });\n  },\n  async removeWatchAsync(watchId): Promise<void> {\n    navigator.geolocation.clearWatch(watchId);\n  },\n  async watchDeviceHeading(headingId): Promise<void> {\n    console.warn('Location.watchDeviceHeading: is not supported on web');\n  },\n  async hasServicesEnabledAsync(): Promise<boolean> {\n    return 'geolocation' in navigator;\n  },\n  async geocodeAsync(): Promise<any[]> {\n    throw new GeocoderError();\n  },\n  async reverseGeocodeAsync(): Promise<any[]> {\n    throw new GeocoderError();\n  },\n  async watchPositionImplAsync(watchId: string, options: LocationOptions): Promise<string> {\n    return new Promise<string>((resolve) => {\n      // @ts-ignore: the types here need to be fixed\n      watchId = global.navigator.geolocation.watchPosition(\n        (position) => {\n          lastKnownPosition = geolocationPositionToJSON(position);\n          LocationEventEmitter.emit('Expo.locationChanged', {\n            watchId,\n            location: lastKnownPosition,\n          });\n        },\n        undefined,\n        // @ts-ignore: the options object needs to be fixed\n        options\n      );\n      resolve(watchId);\n    });\n  },\n\n  getPermissionsAsync,\n  async requestPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n  async requestForegroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n  async requestBackgroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n  async getForegroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n  async getBackgroundPermissionsAsync(): Promise<PermissionResponse> {\n    return getPermissionsAsync();\n  },\n\n  // no-op\n  startObserving() {},\n  stopObserving() {},\n};\n"]}