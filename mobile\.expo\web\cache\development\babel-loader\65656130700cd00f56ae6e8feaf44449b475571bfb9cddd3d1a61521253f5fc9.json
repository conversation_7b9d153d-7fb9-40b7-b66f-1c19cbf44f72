{"ast": null, "code": "export default function useBackButton(_) {}", "map": {"version": 3, "names": ["useBackButton", "_"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native\\src\\useBackButton.tsx"], "sourcesContent": ["import type {\n  NavigationContainerRef,\n  ParamListBase,\n} from '@react-navigation/core';\n\nexport default function useBackButton(\n  _: React.RefObject<NavigationContainerRef<ParamListBase>>\n) {\n  // No-op\n  // BackHandler is not available on web\n}\n"], "mappings": "AAKA,eAAe,SAASA,aAAaA,CACnCC,CAAyD,EACzD,CAEA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}