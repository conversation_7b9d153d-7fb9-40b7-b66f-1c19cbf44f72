{"ast": null, "code": "import _createClass from \"@babel/runtime/helpers/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nexport var PrivateValueStore = _createClass(function PrivateValueStore() {\n  _classCallCheck(this, PrivateValueStore);\n});", "map": {"version": 3, "names": ["PrivateValueStore", "_createClass", "_classCallCheck"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\core\\src\\types.tsx"], "sourcesContent": ["import type {\n  DefaultRouterOptions,\n  InitialState,\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n  PartialState,\n  Route,\n} from '@react-navigation/routers';\nimport type * as React from 'react';\n\ndeclare global {\n  // eslint-disable-next-line @typescript-eslint/no-namespace\n  namespace ReactNavigation {\n    // eslint-disable-next-line @typescript-eslint/no-empty-interface\n    interface RootParamList {}\n  }\n}\n\ntype Keyof<T extends {}> = Extract<keyof T, string>;\n\nexport type DefaultNavigatorOptions<\n  ParamList extends ParamListBase,\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n> = DefaultRouterOptions<Keyof<ParamList>> & {\n  /**\n   * Optional ID for the navigator. Can be used with `navigation.getParent(id)` to refer to a parent.\n   */\n  id?: string;\n  /**\n   * Children React Elements to extract the route configuration from.\n   * Only `Screen`, `Group` and `React.Fragment` are supported as children.\n   */\n  children: React.ReactNode;\n  /**\n   * Event listeners for all the screens in the navigator.\n   */\n  screenListeners?:\n    | ScreenListeners<State, EventMap>\n    | ((props: {\n        route: RouteProp<ParamList>;\n        navigation: any;\n      }) => ScreenListeners<State, EventMap>);\n  /**\n   * Default options for all screens under this navigator.\n   */\n  screenOptions?:\n    | ScreenOptions\n    | ((props: {\n        route: RouteProp<ParamList>;\n        navigation: any;\n      }) => ScreenOptions);\n};\n\nexport type EventMapBase = Record<\n  string,\n  { data?: any; canPreventDefault?: boolean }\n>;\n\nexport type EventMapCore<State extends NavigationState> = {\n  focus: { data: undefined };\n  blur: { data: undefined };\n  state: { data: { state: State } };\n  beforeRemove: { data: { action: NavigationAction }; canPreventDefault: true };\n};\n\nexport type EventArg<\n  EventName extends string,\n  CanPreventDefault extends boolean | undefined = false,\n  Data = undefined\n> = {\n  /**\n   * Type of the event (e.g. `focus`, `blur`)\n   */\n  readonly type: EventName;\n  readonly target?: string;\n} & (CanPreventDefault extends true\n  ? {\n      /**\n       * Whether `event.preventDefault()` was called on this event object.\n       */\n      readonly defaultPrevented: boolean;\n      /**\n       * Prevent the default action which happens on this event.\n       */\n      preventDefault(): void;\n    }\n  : {}) &\n  (undefined extends Data\n    ? { readonly data?: Readonly<Data> }\n    : { readonly data: Readonly<Data> });\n\nexport type EventListenerCallback<\n  EventMap extends EventMapBase,\n  EventName extends keyof EventMap\n> = (\n  e: EventArg<\n    Extract<EventName, string>,\n    EventMap[EventName]['canPreventDefault'],\n    EventMap[EventName]['data']\n  >\n) => void;\n\nexport type EventConsumer<EventMap extends EventMapBase> = {\n  /**\n   * Subscribe to events from the parent navigator.\n   *\n   * @param type Type of the event (e.g. `focus`, `blur`)\n   * @param callback Callback listener which is executed upon receiving the event.\n   */\n  addListener<EventName extends Keyof<EventMap>>(\n    type: EventName,\n    callback: EventListenerCallback<EventMap, EventName>\n  ): () => void;\n  removeListener<EventName extends Keyof<EventMap>>(\n    type: EventName,\n    callback: EventListenerCallback<EventMap, EventName>\n  ): void;\n};\n\nexport type EventEmitter<EventMap extends EventMapBase> = {\n  /**\n   * Emit an event to child screens.\n   *\n   * @param options.type Type of the event (e.g. `focus`, `blur`)\n   * @param [options.data] Optional information regarding the event.\n   * @param [options.target] Key of the target route which should receive the event.\n   * If not specified, all routes receive the event.\n   */\n  emit<EventName extends Keyof<EventMap>>(\n    options: {\n      type: EventName;\n      target?: string;\n    } & (EventMap[EventName]['canPreventDefault'] extends true\n      ? { canPreventDefault: true }\n      : {}) &\n      (undefined extends EventMap[EventName]['data']\n        ? { data?: EventMap[EventName]['data'] }\n        : { data: EventMap[EventName]['data'] })\n  ): EventArg<\n    EventName,\n    EventMap[EventName]['canPreventDefault'],\n    EventMap[EventName]['data']\n  >;\n};\n\nexport class PrivateValueStore<T extends [any, any, any]> {\n  /**\n   * UGLY HACK! DO NOT USE THE TYPE!!!\n   *\n   * TypeScript requires a type to be used to be able to infer it.\n   * The type should exist as its own without any operations such as union.\n   * So we need to figure out a way to store this type in a property.\n   * The problem with a normal property is that it shows up in intelliSense.\n   * Adding private keyword works, but the annotation is stripped away in declaration.\n   * Turns out if we use an empty string, it doesn't show up in intelliSense.\n   */\n  protected ''?: T;\n}\n\ntype NavigationHelpersCommon<\n  ParamList extends ParamListBase,\n  State extends NavigationState = NavigationState\n> = {\n  /**\n   * Dispatch an action or an update function to the router.\n   * The update function will receive the current state,\n   *\n   * @param action Action object or update function.\n   */\n  dispatch(\n    action: NavigationAction | ((state: State) => NavigationAction)\n  ): void;\n\n  /**\n   * Navigate to a route in current navigation tree.\n   *\n   * @param name Name of the route to navigate to.\n   * @param [params] Params object for the route.\n   */\n  navigate<RouteName extends keyof ParamList>(\n    ...args: // this first condition allows us to iterate over a union type\n    // This is to avoid getting a union of all the params from `ParamList[RouteName]`,\n    // which will get our types all mixed up if a union RouteName is passed in.\n    RouteName extends unknown\n      ? // This condition checks if the params are optional,\n        // which means it's either undefined or a union with undefined\n        undefined extends ParamList[RouteName]\n        ?\n            | [screen: RouteName] // if the params are optional, we don't have to provide it\n            | [screen: RouteName, params: ParamList[RouteName]]\n        : [screen: RouteName, params: ParamList[RouteName]]\n      : never\n  ): void;\n\n  /**\n   * Navigate to a route in current navigation tree.\n   *\n   * @param route Object with `key` or `name` for the route to navigate to, and a `params` object.\n   */\n  navigate<RouteName extends keyof ParamList>(\n    options: RouteName extends unknown\n      ?\n          | { key: string; params?: ParamList[RouteName]; merge?: boolean }\n          | {\n              name: RouteName;\n              key?: string;\n              params: ParamList[RouteName];\n              merge?: boolean;\n            }\n      : never\n  ): void;\n\n  /**\n   * Reset the navigation state to the provided state.\n   *\n   * @param state Navigation state object.\n   */\n  reset(state: PartialState<State> | State): void;\n\n  /**\n   * Go back to the previous route in history.\n   */\n  goBack(): void;\n\n  /**\n   * Check if the screen is focused. The method returns `true` if focused, `false` otherwise.\n   * Note that this method doesn't re-render screen when the focus changes. So don't use it in `render`.\n   * To get notified of focus changes, use `addListener('focus', cb)` and `addListener('blur', cb)`.\n   * To conditionally render content based on focus state, use the `useIsFocused` hook.\n   */\n  isFocused(): boolean;\n\n  /**\n   * Check if dispatching back action will be handled by navigation.\n   * Note that this method doesn't re-render screen when the result changes. So don't use it in `render`.\n   */\n  canGoBack(): boolean;\n\n  /**\n   * Returns the name of the navigator specified in the `name` prop.\n   * If no name is specified, returns `undefined`.\n   */\n  getId(): string | undefined;\n\n  /**\n   * Returns the navigation helpers from a parent navigator based on the ID.\n   * If an ID is provided, the navigation helper from the parent navigator with matching ID (including current) will be returned.\n   * If no ID is provided, the navigation helper from the immediate parent navigator will be returned.\n   *\n   * @param id Optional ID of a parent navigator.\n   */\n  getParent<T = NavigationHelpers<ParamListBase> | undefined>(id?: string): T;\n\n  /**\n   * Returns the navigator's state.\n   * Note that this method doesn't re-render screen when the result changes. So don't use it in `render`.\n   */\n  getState(): State;\n} & PrivateValueStore<[ParamList, unknown, unknown]>;\n\nexport type NavigationHelpers<\n  ParamList extends ParamListBase,\n  EventMap extends EventMapBase = {}\n> = NavigationHelpersCommon<ParamList> &\n  EventEmitter<EventMap> & {\n    /**\n     * Update the param object for the route.\n     * The new params will be shallow merged with the old one.\n     *\n     * @param params Params object for the current route.\n     */\n    setParams<RouteName extends keyof ParamList>(\n      params: Partial<ParamList[RouteName]>\n    ): void;\n  };\n\nexport type NavigationContainerProps = {\n  /**\n   * Initial navigation state for the child navigators.\n   */\n  initialState?: InitialState;\n  /**\n   * Callback which is called with the latest navigation state when it changes.\n   */\n  onStateChange?: (state: NavigationState | undefined) => void;\n  /**\n   * Callback which is called when an action is not handled.\n   */\n  onUnhandledAction?: (action: NavigationAction) => void;\n  /**\n   * Whether this navigation container should be independent of parent containers.\n   * If this is not set to `true`, this container cannot be nested inside another container.\n   * Setting it to `true` disconnects any children navigators from parent container.\n   */\n  independent?: boolean;\n  /**\n   * Children elements to render.\n   */\n  children: React.ReactNode;\n};\n\nexport type NavigationProp<\n  ParamList extends {},\n  RouteName extends keyof ParamList = Keyof<ParamList>,\n  NavigatorID extends string | undefined = undefined,\n  State extends NavigationState = NavigationState<ParamList>,\n  ScreenOptions extends {} = {},\n  EventMap extends EventMapBase = {}\n> = Omit<NavigationHelpersCommon<ParamList, State>, 'getParent'> & {\n  /**\n   * Returns the navigation prop from a parent navigator based on the ID.\n   * If an ID is provided, the navigation prop from the parent navigator with matching ID (including current) will be returned.\n   * If no ID is provided, the navigation prop from the immediate parent navigator will be returned.\n   *\n   * @param id Optional ID of a parent navigator.\n   */\n  getParent<T = NavigationProp<ParamListBase> | undefined>(id?: NavigatorID): T;\n\n  /**\n   * Update the param object for the route.\n   * The new params will be shallow merged with the old one.\n   *\n   * @param params Params object for the current route.\n   */\n  setParams(\n    params: ParamList[RouteName] extends undefined\n      ? undefined\n      : Partial<ParamList[RouteName]>\n  ): void;\n\n  /**\n   * Update the options for the route.\n   * The options object will be shallow merged with default options object.\n   *\n   * @param options Options object for the route.\n   */\n  setOptions(options: Partial<ScreenOptions>): void;\n} & EventConsumer<EventMap & EventMapCore<State>> &\n  PrivateValueStore<[ParamList, RouteName, EventMap]>;\n\nexport type RouteProp<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList = Keyof<ParamList>\n> = Route<Extract<RouteName, string>, ParamList[RouteName]>;\n\nexport type CompositeNavigationProp<\n  A extends NavigationProp<ParamListBase, string, any, any, any>,\n  B extends NavigationHelpersCommon<ParamListBase, any>\n> = Omit<A & B, keyof NavigationProp<any>> &\n  NavigationProp<\n    /**\n     * Param list from both navigation objects needs to be combined\n     * For example, we should be able to navigate to screens in both A and B\n     */\n    (A extends NavigationHelpersCommon<infer T> ? T : never) &\n      (B extends NavigationHelpersCommon<infer U> ? U : never),\n    /**\n     * The route name should refer to the route name specified in the first type\n     * Ideally it should work for any of them, but it's not possible to infer that way\n     */\n    A extends NavigationProp<any, infer R> ? R : string,\n    /**\n     * ID from both navigation objects needs to be combined for `getParent`\n     */\n    | (A extends NavigationProp<any, any, infer I> ? I : never)\n    | (B extends NavigationProp<any, any, infer J> ? J : never),\n    /**\n     * The type of state should refer to the state specified in the first type\n     */\n    A extends NavigationProp<any, any, any, infer S> ? S : NavigationState,\n    /**\n     * Screen options from both navigation objects needs to be combined\n     * This allows typechecking `setOptions`\n     */\n    (A extends NavigationProp<any, any, any, any, infer O> ? O : {}) &\n      (B extends NavigationProp<any, any, any, any, infer P> ? P : {}),\n    /**\n     * Event consumer config should refer to the config specified in the first type\n     * This allows typechecking `addListener`/`removeListener`\n     */\n    A extends NavigationProp<any, any, any, any, any, infer E> ? E : {}\n  >;\n\nexport type CompositeScreenProps<\n  A extends {\n    navigation: NavigationProp<\n      ParamListBase,\n      string,\n      string | undefined,\n      any,\n      any,\n      any\n    >;\n    route: RouteProp<ParamListBase>;\n  },\n  B extends {\n    navigation: NavigationHelpersCommon<any, any>;\n  }\n> = {\n  navigation: CompositeNavigationProp<A['navigation'], B['navigation']>;\n  route: A['route'];\n};\n\nexport type Descriptor<\n  ScreenOptions extends {},\n  Navigation extends NavigationProp<any, any, any, any, any, any>,\n  Route extends RouteProp<any, any>\n> = {\n  /**\n   * Render the component associated with this route.\n   */\n  render(): JSX.Element;\n\n  /**\n   * Options for the route.\n   */\n  options: ScreenOptions;\n\n  /**\n   * Route object for the screen\n   */\n  route: Route;\n\n  /**\n   * Navigation object for the screen\n   */\n  navigation: Navigation;\n};\n\nexport type ScreenListeners<\n  State extends NavigationState,\n  EventMap extends EventMapBase\n> = Partial<{\n  [EventName in keyof (EventMap & EventMapCore<State>)]: EventListenerCallback<\n    EventMap & EventMapCore<State>,\n    EventName\n  >;\n}>;\n\ntype ScreenComponentType<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList\n> =\n  | React.ComponentType<{\n      route: RouteProp<ParamList, RouteName>;\n      navigation: any;\n    }>\n  | React.ComponentType<{}>;\n\nexport type RouteConfigComponent<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList\n> =\n  | {\n      /**\n       * React component to render for this screen.\n       */\n      component: ScreenComponentType<ParamList, RouteName>;\n      getComponent?: never;\n      children?: never;\n    }\n  | {\n      /**\n       * Lazily get a React component to render for this screen.\n       */\n      getComponent: () => ScreenComponentType<ParamList, RouteName>;\n      component?: never;\n      children?: never;\n    }\n  | {\n      /**\n       * Render callback to render content of this screen.\n       */\n      children: (props: {\n        route: RouteProp<ParamList, RouteName>;\n        navigation: any;\n      }) => React.ReactNode;\n      component?: never;\n      getComponent?: never;\n    };\n\nexport type RouteConfig<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList,\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase\n> = {\n  /**\n   * Optional key for this screen. This doesn't need to be unique.\n   * If the key changes, existing screens with this name will be removed or reset.\n   * Useful when we have some common screens and have conditional rendering.\n   */\n  navigationKey?: string;\n\n  /**\n   * Route name of this screen.\n   */\n  name: RouteName;\n\n  /**\n   * Navigator options for this screen.\n   */\n  options?:\n    | ScreenOptions\n    | ((props: {\n        route: RouteProp<ParamList, RouteName>;\n        navigation: any;\n      }) => ScreenOptions);\n\n  /**\n   * Event listeners for this screen.\n   */\n  listeners?:\n    | ScreenListeners<State, EventMap>\n    | ((props: {\n        route: RouteProp<ParamList, RouteName>;\n        navigation: any;\n      }) => ScreenListeners<State, EventMap>);\n\n  /**\n   * Function to return an unique ID for this screen.\n   * Receives an object with the route params.\n   * For a given screen name, there will always be only one screen corresponding to an ID.\n   * If `undefined` is returned, it acts same as no `getId` being specified.\n   */\n  getId?: ({ params }: { params: ParamList[RouteName] }) => string | undefined;\n\n  /**\n   * Initial params object for the route.\n   */\n  initialParams?: Partial<ParamList[RouteName]>;\n} & RouteConfigComponent<ParamList, RouteName>;\n\nexport type RouteGroupConfig<\n  ParamList extends ParamListBase,\n  ScreenOptions extends {}\n> = {\n  /**\n   * Optional key for the screens in this group.\n   * If the key changes, all existing screens in this group will be removed or reset.\n   */\n  navigationKey?: string;\n\n  /**\n   * Navigator options for this screen.\n   */\n  screenOptions?:\n    | ScreenOptions\n    | ((props: {\n        route: RouteProp<ParamList, keyof ParamList>;\n        navigation: any;\n      }) => ScreenOptions);\n  /**\n   * Children React Elements to extract the route configuration from.\n   * Only `Screen`, `Group` and `React.Fragment` are supported as children.\n   */\n  children: React.ReactNode;\n};\n\nexport type NavigationContainerEventMap = {\n  /**\n   * Event which fires when the navigation state changes.\n   */\n  state: {\n    data: {\n      /**\n       * The updated state object after the state change.\n       */\n      state: NavigationState | PartialState<NavigationState> | undefined;\n    };\n  };\n  /**\n   * Event which fires when current options changes.\n   */\n  options: { data: { options: object } };\n  /**\n   * Event which fires when an action is dispatched.\n   * Only intended for debugging purposes, don't use it for app logic.\n   * This event will be emitted before state changes have been applied.\n   */\n  __unsafe_action__: {\n    data: {\n      /**\n       * The action object which was dispatched.\n       */\n      action: NavigationAction;\n      /**\n       * Whether the action was a no-op, i.e. resulted any state changes.\n       */\n      noop: boolean;\n      /**\n       * Stack trace of the action, this will only be available during development.\n       */\n      stack: string | undefined;\n    };\n  };\n};\n\nexport type NavigationContainerRef<ParamList extends {}> =\n  NavigationHelpers<ParamList> &\n    EventConsumer<NavigationContainerEventMap> & {\n      /**\n       * Reset the navigation state of the root navigator to the provided state.\n       *\n       * @param state Navigation state object.\n       */\n      resetRoot(state?: PartialState<NavigationState> | NavigationState): void;\n      /**\n       * Get the rehydrated navigation state of the navigation tree.\n       */\n      getRootState(): NavigationState;\n      /**\n       * Get the currently focused navigation route.\n       */\n      getCurrentRoute(): Route<string> | undefined;\n      /**\n       * Get the currently focused route's options.\n       */\n      getCurrentOptions(): object | undefined;\n      /**\n       * Whether the navigation container is ready to handle actions.\n       */\n      isReady(): boolean;\n      /**\n       * Stub function for setOptions on navigation object for use with useNavigation.\n       */\n      setOptions(): never;\n      /**\n       * Stub function for getParent on navigation object for use with useNavigation.\n       */\n      getParent(): undefined;\n    };\n\nexport type NavigationContainerRefWithCurrent<ParamList extends {}> =\n  NavigationContainerRef<ParamList> & {\n    current: NavigationContainerRef<ParamList> | null;\n  };\n\nexport type TypedNavigator<\n  ParamList extends ParamListBase,\n  State extends NavigationState,\n  ScreenOptions extends {},\n  EventMap extends EventMapBase,\n  Navigator extends React.ComponentType<any>\n> = {\n  /**\n   * Navigator component which manages the child screens.\n   */\n  Navigator: React.ComponentType<\n    Omit<\n      React.ComponentProps<Navigator>,\n      keyof DefaultNavigatorOptions<any, any, any, any>\n    > &\n      DefaultNavigatorOptions<ParamList, State, ScreenOptions, EventMap>\n  >;\n  /**\n   * Component used for grouping multiple route configuration.\n   */\n  Group: React.ComponentType<RouteGroupConfig<ParamList, ScreenOptions>>;\n  /**\n   * Component used for specifying route configuration.\n   */\n  Screen: <RouteName extends keyof ParamList>(\n    _: RouteConfig<ParamList, RouteName, State, ScreenOptions, EventMap>\n  ) => null;\n};\n\nexport type NavigatorScreenParams<\n  ParamList,\n  State extends NavigationState = NavigationState\n> =\n  | {\n      screen?: never;\n      params?: never;\n      initial?: never;\n      path?: string;\n      state: PartialState<State> | State | undefined;\n    }\n  | {\n      [RouteName in keyof ParamList]: undefined extends ParamList[RouteName]\n        ? {\n            screen: RouteName;\n            params?: ParamList[RouteName];\n            initial?: boolean;\n            path?: string;\n            state?: never;\n          }\n        : {\n            screen: RouteName;\n            params: ParamList[RouteName];\n            initial?: boolean;\n            path?: string;\n            state?: never;\n          };\n    }[keyof ParamList];\n\nexport type PathConfig<ParamList extends {}> = {\n  path?: string;\n  exact?: boolean;\n  parse?: Record<string, (value: string) => any>;\n  stringify?: Record<string, (value: any) => string>;\n  screens?: PathConfigMap<ParamList>;\n  initialRouteName?: keyof ParamList;\n};\n\nexport type PathConfigMap<ParamList extends {}> = {\n  [RouteName in keyof ParamList]?: NonNullable<\n    ParamList[RouteName]\n  > extends NavigatorScreenParams<infer T extends {}>\n    ? string | PathConfig<T>\n    : string | Omit<PathConfig<{}>, 'screens' | 'initialRouteName'>;\n};\n"], "mappings": ";;AAoJA,WAAaA,iBAAiB,GAAAC,YAAA,UAAAD,kBAAA;EAAAE,eAAA,OAAAF,iBAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}