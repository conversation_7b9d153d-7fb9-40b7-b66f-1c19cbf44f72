{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nvar _excluded = [\"get\", \"request\"];\nimport { useCallback, useEffect, useRef, useState } from 'react';\nfunction usePermission(methods, options) {\n  var isMounted = useRef(true);\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    status = _useState2[0],\n    setStatus = _useState2[1];\n  var _ref = options || {},\n    _ref$get = _ref.get,\n    get = _ref$get === void 0 ? true : _ref$get,\n    _ref$request = _ref.request,\n    request = _ref$request === void 0 ? false : _ref$request,\n    permissionOptions = _objectWithoutProperties(_ref, _excluded);\n  var getPermission = useCallback(_asyncToGenerator(function* () {\n    var response = yield methods.getMethod(Object.keys(permissionOptions).length > 0 ? permissionOptions : undefined);\n    if (isMounted.current) setStatus(response);\n    return response;\n  }), [methods.getMethod]);\n  var requestPermission = useCallback(_asyncToGenerator(function* () {\n    var response = yield methods.requestMethod(Object.keys(permissionOptions).length > 0 ? permissionOptions : undefined);\n    if (isMounted.current) setStatus(response);\n    return response;\n  }), [methods.requestMethod]);\n  useEffect(function runMethods() {\n    if (request) requestPermission();\n    if (!request && get) getPermission();\n  }, [get, request, requestPermission, getPermission]);\n  useEffect(function didMount() {\n    isMounted.current = true;\n    return function () {\n      isMounted.current = false;\n    };\n  }, []);\n  return [status, requestPermission, getPermission];\n}\nexport function createPermissionHook(methods) {\n  return function (options) {\n    return usePermission(methods, options);\n  };\n}", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useState", "usePermission", "methods", "options", "isMounted", "_useState", "_useState2", "_slicedToArray", "status", "setStatus", "_ref", "_ref$get", "get", "_ref$request", "request", "permissionOptions", "_objectWithoutProperties", "_excluded", "getPermission", "_asyncToGenerator", "response", "getMethod", "Object", "keys", "length", "undefined", "current", "requestPermission", "requestMethod", "runMethods", "didMount", "createPermissionHook"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\expo-modules-core\\src\\PermissionsHook.ts"], "sourcesContent": ["import { useCallback, useEffect, useRef, useState } from 'react';\n\nimport { PermissionResponse } from './PermissionsInterface';\n\n// These types are identical, but improves the readability for suggestions in editors\ntype RequestPermissionMethod<Permission extends PermissionResponse> = () => Promise<Permission>;\ntype GetPermissionMethod<Permission extends PermissionResponse> = () => Promise<Permission>;\n\ninterface PermissionHookMethods<Permission extends PermissionResponse, Options = never> {\n  /** The permission method that requests the user to grant permission. */\n  requestMethod: (options?: Options) => Promise<Permission>;\n  /** The permission method that only fetches the current permission status. */\n  getMethod: (options?: Options) => Promise<Permission>;\n}\n\ninterface PermissionHookBehavior {\n  /** If the hook should automatically fetch the current permission status, without asking the user. */\n  get?: boolean;\n  /** If the hook should automatically request the user to grant permission. */\n  request?: boolean;\n}\n\nexport type PermissionHookOptions<Options extends object> = PermissionHookBehavior & Options;\n\n/**\n * Get or request permission for protected functionality within the app.\n * It uses separate permission requesters to interact with a single permission.\n * By default, the hook will only retrieve the permission status.\n */\nfunction usePermission<Permission extends PermissionResponse, Options extends object>(\n  methods: PermissionHookMethods<Permission, Options>,\n  options?: PermissionHookOptions<Options>\n): [Permission | null, RequestPermissionMethod<Permission>, GetPermissionMethod<Permission>] {\n  const isMounted = useRef(true);\n  const [status, setStatus] = useState<Permission | null>(null);\n  const { get = true, request = false, ...permissionOptions } = options || {};\n\n  const getPermission = useCallback(async () => {\n    const response = await methods.getMethod(\n      Object.keys(permissionOptions).length > 0 ? (permissionOptions as Options) : undefined\n    );\n    if (isMounted.current) setStatus(response);\n    return response;\n  }, [methods.getMethod]);\n\n  const requestPermission = useCallback(async () => {\n    const response = await methods.requestMethod(\n      Object.keys(permissionOptions).length > 0 ? (permissionOptions as Options) : undefined\n    );\n    if (isMounted.current) setStatus(response);\n    return response;\n  }, [methods.requestMethod]);\n\n  useEffect(\n    function runMethods() {\n      if (request) requestPermission();\n      if (!request && get) getPermission();\n    },\n    [get, request, requestPermission, getPermission]\n  );\n\n  // Workaround for unmounting components receiving state updates\n  useEffect(function didMount() {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n\n  return [status, requestPermission, getPermission];\n}\n\n/**\n * Create a new permission hook with the permission methods built-in.\n * This can be used to quickly create specific permission hooks in every module.\n */\nexport function createPermissionHook<Permission extends PermissionResponse, Options extends object>(\n  methods: PermissionHookMethods<Permission, Options>\n) {\n  return (options?: PermissionHookOptions<Options>) =>\n    usePermission<Permission, Options>(methods, options);\n}\n"], "mappings": ";;;;AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AA6BhE,SAASC,aAAaA,CACpBC,OAAmD,EACnDC,OAAwC;EAExC,IAAMC,SAAS,GAAGL,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAAM,SAAA,GAA4BL,QAAQ,CAAoB,IAAI,CAAC;IAAAM,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAAtDG,MAAM,GAAAF,UAAA;IAAEG,SAAS,GAAAH,UAAA;EACxB,IAAAI,IAAA,GAA8DP,OAAO,IAAI,EAAE;IAAAQ,QAAA,GAAAD,IAAA,CAAnEE,GAAG;IAAHA,GAAG,GAAAD,QAAA,cAAG,IAAI,GAAAA,QAAA;IAAAE,YAAA,GAAAH,IAAA,CAAEI,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;IAAKE,iBAAiB,GAAAC,wBAAA,CAAAN,IAAA,EAAAO,SAAA;EAEzD,IAAMC,aAAa,GAAGrB,WAAW,CAAAsB,iBAAA,CAAC,aAAW;IAC3C,IAAMC,QAAQ,SAASlB,OAAO,CAACmB,SAAS,CACtCC,MAAM,CAACC,IAAI,CAACR,iBAAiB,CAAC,CAACS,MAAM,GAAG,CAAC,GAAIT,iBAA6B,GAAGU,SAAS,CACvF;IACD,IAAIrB,SAAS,CAACsB,OAAO,EAAEjB,SAAS,CAACW,QAAQ,CAAC;IAC1C,OAAOA,QAAQ;EACjB,CAAC,GAAE,CAAClB,OAAO,CAACmB,SAAS,CAAC,CAAC;EAEvB,IAAMM,iBAAiB,GAAG9B,WAAW,CAAAsB,iBAAA,CAAC,aAAW;IAC/C,IAAMC,QAAQ,SAASlB,OAAO,CAAC0B,aAAa,CAC1CN,MAAM,CAACC,IAAI,CAACR,iBAAiB,CAAC,CAACS,MAAM,GAAG,CAAC,GAAIT,iBAA6B,GAAGU,SAAS,CACvF;IACD,IAAIrB,SAAS,CAACsB,OAAO,EAAEjB,SAAS,CAACW,QAAQ,CAAC;IAC1C,OAAOA,QAAQ;EACjB,CAAC,GAAE,CAAClB,OAAO,CAAC0B,aAAa,CAAC,CAAC;EAE3B9B,SAAS,CACP,SAAS+B,UAAUA,CAAA;IACjB,IAAIf,OAAO,EAAEa,iBAAiB,EAAE;IAChC,IAAI,CAACb,OAAO,IAAIF,GAAG,EAAEM,aAAa,EAAE;EACtC,CAAC,EACD,CAACN,GAAG,EAAEE,OAAO,EAAEa,iBAAiB,EAAET,aAAa,CAAC,CACjD;EAGDpB,SAAS,CAAC,SAASgC,QAAQA,CAAA;IACzB1B,SAAS,CAACsB,OAAO,GAAG,IAAI;IACxB,OAAO,YAAK;MACVtB,SAAS,CAACsB,OAAO,GAAG,KAAK;IAC3B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO,CAAClB,MAAM,EAAEmB,iBAAiB,EAAET,aAAa,CAAC;AACnD;AAMA,OAAM,SAAUa,oBAAoBA,CAClC7B,OAAmD;EAEnD,OAAO,UAACC,OAAwC;IAAA,OAC9CF,aAAa,CAAsBC,OAAO,EAAEC,OAAO,CAAC;EAAA;AACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}