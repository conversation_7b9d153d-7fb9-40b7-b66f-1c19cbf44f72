{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { getPathFromState, NavigationHelpersContext } from '@react-navigation/core';\nimport * as React from 'react';\nimport LinkingContext from \"./LinkingContext\";\nvar _getRootStateForNavigate = function getRootStateForNavigate(navigation, state) {\n  var parent = navigation.getParent();\n  if (parent) {\n    var parentState = parent.getState();\n    return _getRootStateForNavigate(parent, {\n      index: 0,\n      routes: [_objectSpread(_objectSpread({}, parentState.routes[parentState.index]), {}, {\n        state: state\n      })]\n    });\n  }\n  return state;\n};\nexport default function useLinkBuilder() {\n  var navigation = React.useContext(NavigationHelpersContext);\n  var linking = React.useContext(LinkingContext);\n  var buildLink = React.useCallback(function (name, params) {\n    var options = linking.options;\n    if ((options === null || options === void 0 ? void 0 : options.enabled) === false) {\n      return undefined;\n    }\n    var state = navigation ? _getRootStateForNavigate(navigation, {\n      index: 0,\n      routes: [{\n        name: name,\n        params: params\n      }]\n    }) : {\n      index: 0,\n      routes: [{\n        name: name,\n        params: params\n      }]\n    };\n    var path = options !== null && options !== void 0 && options.getPathFromState ? options.getPathFromState(state, options === null || options === void 0 ? void 0 : options.config) : getPathFromState(state, options === null || options === void 0 ? void 0 : options.config);\n    return path;\n  }, [linking, navigation]);\n  return buildLink;\n}", "map": {"version": 3, "names": ["getPathFromState", "NavigationHelpersContext", "React", "LinkingContext", "getRootStateForNavigate", "navigation", "state", "parent", "getParent", "parentState", "getState", "index", "routes", "_objectSpread", "useLinkBuilder", "useContext", "linking", "buildLink", "useCallback", "name", "params", "options", "enabled", "undefined", "path", "config"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native\\src\\useLinkBuilder.tsx"], "sourcesContent": ["import {\n  getPathFromState,\n  NavigationHelpers,\n  NavigationHelpersContext,\n  NavigationProp,\n  ParamListBase,\n} from '@react-navigation/core';\nimport * as React from 'react';\n\nimport LinkingContext from './LinkingContext';\n\ntype NavigationObject =\n  | NavigationHelpers<ParamListBase>\n  | NavigationProp<ParamListBase>;\n\ntype MinimalState = {\n  index: number;\n  routes: { name: string; params?: object; state?: MinimalState }[];\n};\n\nconst getRootStateForNavigate = (\n  navigation: NavigationObject,\n  state: MinimalState\n): MinimalState => {\n  const parent = navigation.getParent();\n\n  if (parent) {\n    const parentState = parent.getState();\n\n    return getRootStateForNavigate(parent, {\n      index: 0,\n      routes: [\n        {\n          ...parentState.routes[parentState.index],\n          state: state,\n        },\n      ],\n    });\n  }\n\n  return state;\n};\n\n/**\n * Build destination link for a navigate action.\n * Useful for showing anchor tags on the web for buttons that perform navigation.\n */\nexport default function useLinkBuilder() {\n  const navigation = React.useContext(NavigationHelpersContext);\n  const linking = React.useContext(LinkingContext);\n\n  const buildLink = React.useCallback(\n    (name: string, params?: object) => {\n      const { options } = linking;\n\n      if (options?.enabled === false) {\n        return undefined;\n      }\n\n      const state = navigation\n        ? getRootStateForNavigate(navigation, {\n            index: 0,\n            routes: [{ name, params }],\n          })\n        : // If we couldn't find a navigation object in context, we're at root\n          // So we'll construct a basic state object to use\n          {\n            index: 0,\n            routes: [{ name, params }],\n          };\n\n      const path = options?.getPathFromState\n        ? options.getPathFromState(state, options?.config)\n        : getPathFromState(state, options?.config);\n\n      return path;\n    },\n    [linking, navigation]\n  );\n\n  return buildLink;\n}\n"], "mappings": ";;;AAAA,SACEA,gBAAgB,EAEhBC,wBAAwB,QAGnB,wBAAwB;AAC/B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,cAAc;AAWrB,IAAMC,wBAAuB,GAAG,SAA1BA,uBAAuBA,CAC3BC,UAA4B,EAC5BC,KAAmB,EACF;EACjB,IAAMC,MAAM,GAAGF,UAAU,CAACG,SAAS,EAAE;EAErC,IAAID,MAAM,EAAE;IACV,IAAME,WAAW,GAAGF,MAAM,CAACG,QAAQ,EAAE;IAErC,OAAON,wBAAuB,CAACG,MAAM,EAAE;MACrCI,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAAC,aAAA,CAAAA,aAAA,KAEDJ,WAAW,CAACG,MAAM,CAACH,WAAW,CAACE,KAAK,CAAC;QACxCL,KAAK,EAAEA;MAAA;IAGb,CAAC,CAAC;EACJ;EAEA,OAAOA,KAAK;AACd,CAAC;AAMD,eAAe,SAASQ,cAAcA,CAAA,EAAG;EACvC,IAAMT,UAAU,GAAGH,KAAK,CAACa,UAAU,CAACd,wBAAwB,CAAC;EAC7D,IAAMe,OAAO,GAAGd,KAAK,CAACa,UAAU,CAACZ,cAAc,CAAC;EAEhD,IAAMc,SAAS,GAAGf,KAAK,CAACgB,WAAW,CACjC,UAACC,IAAY,EAAEC,MAAe,EAAK;IACjC,IAAQC,OAAA,GAAYL,OAAO,CAAnBK,OAAA;IAER,IAAI,CAAAA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,OAAO,MAAK,KAAK,EAAE;MAC9B,OAAOC,SAAS;IAClB;IAEA,IAAMjB,KAAK,GAAGD,UAAU,GACpBD,wBAAuB,CAACC,UAAU,EAAE;MAClCM,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;QAAEO,IAAI,EAAJA,IAAI;QAAEC,MAAA,EAAAA;MAAO,CAAC;IAC3B,CAAC,CAAC,GAGF;MACET,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;QAAEO,IAAI,EAAJA,IAAI;QAAEC,MAAA,EAAAA;MAAO,CAAC;IAC3B,CAAC;IAEL,IAAMI,IAAI,GAAGH,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAErB,gBAAgB,GAClCqB,OAAO,CAACrB,gBAAgB,CAACM,KAAK,EAAEe,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,MAAM,CAAC,GAChDzB,gBAAgB,CAACM,KAAK,EAAEe,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEI,MAAM,CAAC;IAE5C,OAAOD,IAAI;EACb,CAAC,EACD,CAACR,OAAO,EAAEX,UAAU,CAAC,CACtB;EAED,OAAOY,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}