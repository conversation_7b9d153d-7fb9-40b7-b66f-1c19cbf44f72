{"ast": null, "code": "import useLayoutEffect from \"../useLayoutEffect\";\nimport UIManager from \"../../exports/UIManager\";\nimport canUseDOM from \"../canUseDom\";\nvar DOM_LAYOUT_HANDLER_NAME = '__reactLayoutHandler';\nvar didWarn = !canUseDOM;\nvar resizeObserver = null;\nfunction getResizeObserver() {\n  if (canUseDOM && typeof window.ResizeObserver !== 'undefined') {\n    if (resizeObserver == null) {\n      resizeObserver = new window.ResizeObserver(function (entries) {\n        entries.forEach(function (entry) {\n          var node = entry.target;\n          var onLayout = node[DOM_LAYOUT_HANDLER_NAME];\n          if (typeof onLayout === 'function') {\n            UIManager.measure(node, function (x, y, width, height, left, top) {\n              var event = {\n                nativeEvent: {\n                  layout: {\n                    x: x,\n                    y: y,\n                    width: width,\n                    height: height,\n                    left: left,\n                    top: top\n                  }\n                },\n                timeStamp: Date.now()\n              };\n              Object.defineProperty(event.nativeEvent, 'target', {\n                enumerable: true,\n                get: function get() {\n                  return entry.target;\n                }\n              });\n              onLayout(event);\n            });\n          }\n        });\n      });\n    }\n  } else if (!didWarn) {\n    if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test') {\n      console.warn('onLayout relies on ResizeObserver which is not supported by your browser. ' + 'Please include a polyfill, e.g., https://github.com/que-etc/resize-observer-polyfill.');\n      didWarn = true;\n    }\n  }\n  return resizeObserver;\n}\nexport default function useElementLayout(ref, onLayout) {\n  var observer = getResizeObserver();\n  useLayoutEffect(function () {\n    var node = ref.current;\n    if (node != null) {\n      node[DOM_LAYOUT_HANDLER_NAME] = onLayout;\n    }\n  }, [ref, onLayout]);\n  useLayoutEffect(function () {\n    var node = ref.current;\n    if (node != null && observer != null) {\n      if (typeof node[DOM_LAYOUT_HANDLER_NAME] === 'function') {\n        observer.observe(node);\n      } else {\n        observer.unobserve(node);\n      }\n    }\n    return function () {\n      if (node != null && observer != null) {\n        observer.unobserve(node);\n      }\n    };\n  }, [ref, observer]);\n}", "map": {"version": 3, "names": ["useLayoutEffect", "UIManager", "canUseDOM", "DOM_LAYOUT_HANDLER_NAME", "<PERSON><PERSON><PERSON><PERSON>", "resizeObserver", "getResizeObserver", "window", "ResizeObserver", "entries", "for<PERSON>ach", "entry", "node", "target", "onLayout", "measure", "x", "y", "width", "height", "left", "top", "event", "nativeEvent", "layout", "timeStamp", "Date", "now", "Object", "defineProperty", "enumerable", "get", "process", "env", "NODE_ENV", "console", "warn", "useElementLayout", "ref", "observer", "current", "observe", "unobserve"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/node_modules/react-native-web/dist/modules/useElementLayout/index.js"], "sourcesContent": ["/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport useLayoutEffect from '../useLayoutEffect';\nimport UIManager from '../../exports/UIManager';\nimport canUseDOM from '../canUseDom';\nvar DOM_LAYOUT_HANDLER_NAME = '__reactLayoutHandler';\nvar didWarn = !canUseDOM;\nvar resizeObserver = null;\nfunction getResizeObserver() {\n  if (canUseDOM && typeof window.ResizeObserver !== 'undefined') {\n    if (resizeObserver == null) {\n      resizeObserver = new window.ResizeObserver(function (entries) {\n        entries.forEach(entry => {\n          var node = entry.target;\n          var onLayout = node[DOM_LAYOUT_HANDLER_NAME];\n          if (typeof onLayout === 'function') {\n            // We still need to measure the view because browsers don't yet provide\n            // border-box dimensions in the entry\n            UIManager.measure(node, (x, y, width, height, left, top) => {\n              var event = {\n                // $FlowFixMe\n                nativeEvent: {\n                  layout: {\n                    x,\n                    y,\n                    width,\n                    height,\n                    left,\n                    top\n                  }\n                },\n                timeStamp: Date.now()\n              };\n              Object.defineProperty(event.nativeEvent, 'target', {\n                enumerable: true,\n                get: () => entry.target\n              });\n              onLayout(event);\n            });\n          }\n        });\n      });\n    }\n  } else if (!didWarn) {\n    if (process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test') {\n      console.warn('onLayout relies on ResizeObserver which is not supported by your browser. ' + 'Please include a polyfill, e.g., https://github.com/que-etc/resize-observer-polyfill.');\n      didWarn = true;\n    }\n  }\n  return resizeObserver;\n}\nexport default function useElementLayout(ref, onLayout) {\n  var observer = getResizeObserver();\n  useLayoutEffect(() => {\n    var node = ref.current;\n    if (node != null) {\n      node[DOM_LAYOUT_HANDLER_NAME] = onLayout;\n    }\n  }, [ref, onLayout]);\n\n  // Observing is done in a separate effect to avoid this effect running\n  // when 'onLayout' changes.\n  useLayoutEffect(() => {\n    var node = ref.current;\n    if (node != null && observer != null) {\n      if (typeof node[DOM_LAYOUT_HANDLER_NAME] === 'function') {\n        observer.observe(node);\n      } else {\n        observer.unobserve(node);\n      }\n    }\n    return () => {\n      if (node != null && observer != null) {\n        observer.unobserve(node);\n      }\n    };\n  }, [ref, observer]);\n}"], "mappings": "AASA,OAAOA,eAAe;AACtB,OAAOC,SAAS;AAChB,OAAOC,SAAS;AAChB,IAAIC,uBAAuB,GAAG,sBAAsB;AACpD,IAAIC,OAAO,GAAG,CAACF,SAAS;AACxB,IAAIG,cAAc,GAAG,IAAI;AACzB,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,IAAIJ,SAAS,IAAI,OAAOK,MAAM,CAACC,cAAc,KAAK,WAAW,EAAE;IAC7D,IAAIH,cAAc,IAAI,IAAI,EAAE;MAC1BA,cAAc,GAAG,IAAIE,MAAM,CAACC,cAAc,CAAC,UAAUC,OAAO,EAAE;QAC5DA,OAAO,CAACC,OAAO,CAAC,UAAAC,KAAK,EAAI;UACvB,IAAIC,IAAI,GAAGD,KAAK,CAACE,MAAM;UACvB,IAAIC,QAAQ,GAAGF,IAAI,CAACT,uBAAuB,CAAC;UAC5C,IAAI,OAAOW,QAAQ,KAAK,UAAU,EAAE;YAGlCb,SAAS,CAACc,OAAO,CAACH,IAAI,EAAE,UAACI,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAK;cAC1D,IAAIC,KAAK,GAAG;gBAEVC,WAAW,EAAE;kBACXC,MAAM,EAAE;oBACNR,CAAC,EAADA,CAAC;oBACDC,CAAC,EAADA,CAAC;oBACDC,KAAK,EAALA,KAAK;oBACLC,MAAM,EAANA,MAAM;oBACNC,IAAI,EAAJA,IAAI;oBACJC,GAAG,EAAHA;kBACF;gBACF,CAAC;gBACDI,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;cACtB,CAAC;cACDC,MAAM,CAACC,cAAc,CAACP,KAAK,CAACC,WAAW,EAAE,QAAQ,EAAE;gBACjDO,UAAU,EAAE,IAAI;gBAChBC,GAAG,EAAE,SAALA,GAAGA,CAAA;kBAAA,OAAQpB,KAAK,CAACE,MAAM;gBAAA;cACzB,CAAC,CAAC;cACFC,QAAQ,CAACQ,KAAK,CAAC;YACjB,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,MAAM,IAAI,CAAClB,OAAO,EAAE;IACnB,IAAI4B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MAC5EC,OAAO,CAACC,IAAI,CAAC,4EAA4E,GAAG,uFAAuF,CAAC;MACpLhC,OAAO,GAAG,IAAI;IAChB;EACF;EACA,OAAOC,cAAc;AACvB;AACA,eAAe,SAASgC,gBAAgBA,CAACC,GAAG,EAAExB,QAAQ,EAAE;EACtD,IAAIyB,QAAQ,GAAGjC,iBAAiB,CAAC,CAAC;EAClCN,eAAe,CAAC,YAAM;IACpB,IAAIY,IAAI,GAAG0B,GAAG,CAACE,OAAO;IACtB,IAAI5B,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,CAACT,uBAAuB,CAAC,GAAGW,QAAQ;IAC1C;EACF,CAAC,EAAE,CAACwB,GAAG,EAAExB,QAAQ,CAAC,CAAC;EAInBd,eAAe,CAAC,YAAM;IACpB,IAAIY,IAAI,GAAG0B,GAAG,CAACE,OAAO;IACtB,IAAI5B,IAAI,IAAI,IAAI,IAAI2B,QAAQ,IAAI,IAAI,EAAE;MACpC,IAAI,OAAO3B,IAAI,CAACT,uBAAuB,CAAC,KAAK,UAAU,EAAE;QACvDoC,QAAQ,CAACE,OAAO,CAAC7B,IAAI,CAAC;MACxB,CAAC,MAAM;QACL2B,QAAQ,CAACG,SAAS,CAAC9B,IAAI,CAAC;MAC1B;IACF;IACA,OAAO,YAAM;MACX,IAAIA,IAAI,IAAI,IAAI,IAAI2B,QAAQ,IAAI,IAAI,EAAE;QACpCA,QAAQ,CAACG,SAAS,CAAC9B,IAAI,CAAC;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAAC0B,GAAG,EAAEC,QAAQ,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}