{"ast": null, "code": "export default function styleToBarStyle() {\n  var style = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'auto';\n  var colorScheme = arguments.length > 1 ? arguments[1] : undefined;\n  if (!colorScheme) {\n    colorScheme = 'light';\n  }\n  var resolvedStyle = style;\n  if (style === 'auto') {\n    resolvedStyle = colorScheme === 'light' ? 'dark' : 'light';\n  } else if (style === 'inverted') {\n    resolvedStyle = colorScheme === 'light' ? 'light' : 'dark';\n  }\n  return resolvedStyle === 'light' ? 'light-content' : 'dark-content';\n}", "map": {"version": 3, "names": ["styleToBarStyle", "style", "arguments", "length", "undefined", "colorScheme", "resolvedStyle"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\expo-status-bar\\src\\styleToBarStyle.web.ts"], "sourcesContent": ["import { StatusBarStyle } from './StatusBar.types';\n\nexport default function styleToBarStyle(\n  style: StatusBarStyle = 'auto',\n  colorScheme: 'light' | 'dark'\n): 'light-content' | 'dark-content' {\n  if (!colorScheme) {\n    colorScheme = 'light';\n  }\n\n  let resolvedStyle = style;\n  if (style === 'auto') {\n    resolvedStyle = colorScheme === 'light' ? 'dark' : 'light';\n  } else if (style === 'inverted') {\n    resolvedStyle = colorScheme === 'light' ? 'light' : 'dark';\n  }\n\n  return resolvedStyle === 'light' ? 'light-content' : 'dark-content';\n}\n"], "mappings": "AAEA,eAAc,SAAUA,eAAeA,CAAA,EAER;EAAA,IAD7BC,KAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAwB,MAAM;EAAA,IAC9BG,WAA6B,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EAE7B,IAAI,CAACC,WAAW,EAAE;IAChBA,WAAW,GAAG,OAAO;;EAGvB,IAAIC,aAAa,GAAGL,KAAK;EACzB,IAAIA,KAAK,KAAK,MAAM,EAAE;IACpBK,aAAa,GAAGD,WAAW,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;GAC3D,MAAM,IAAIJ,KAAK,KAAK,UAAU,EAAE;IAC/BK,aAAa,GAAGD,WAAW,KAAK,OAAO,GAAG,OAAO,GAAG,MAAM;;EAG5D,OAAOC,aAAa,KAAK,OAAO,GAAG,eAAe,GAAG,cAAc;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}