{"ast": null, "code": "export { default as createNativeStackNavigator } from \"./navigators/createNativeStackNavigator\";\nexport { default as NativeStackView } from \"./views/NativeStackView\";", "map": {"version": 3, "names": ["default", "createNativeStackNavigator", "NativeStackView"], "sources": ["C:\\Bagide\\Clients\\Carlos\\GasLink\\mobile\\node_modules\\@react-navigation\\native-stack\\src\\index.tsx"], "sourcesContent": ["/**\n * Navigators\n */\nexport { default as createNativeStackNavigator } from './navigators/createNativeStackNavigator';\n\n/**\n * Views\n */\nexport { default as NativeStackView } from './views/NativeStackView';\n\n/**\n * Types\n */\nexport type {\n  NativeStackHeaderProps,\n  NativeStackNavigationEventMap,\n  NativeStackNavigationOptions,\n  NativeStackNavigationProp,\n  NativeStackScreenProps,\n} from './types';\n"], "mappings": "AAGA,SAASA,OAAO,IAAIC,0BAA0B;AAK9C,SAASD,OAAO,IAAIE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}