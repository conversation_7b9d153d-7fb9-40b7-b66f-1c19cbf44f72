{"ast": null, "code": "import React from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { StatusBar } from 'expo-status-bar';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport HomeScreen from \"./screens/HomeScreen\";\nimport GasStationDetailScreen from \"./screens/GasStationDetailScreen\";\nimport SearchScreen from \"./screens/SearchScreen\";\nimport ProfileScreen from \"./screens/ProfileScreen\";\nimport LoginScreen from \"./screens/LoginScreen\";\nimport RegisterScreen from \"./screens/RegisterScreen\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Stack = createNativeStackNavigator();\nexport default function App() {\n  return _jsx(NavigationContainer, {\n    children: _jsxs(View, {\n      style: styles.container,\n      children: [_jsx(StatusBar, {\n        style: \"auto\"\n      }), _jsxs(Stack.Navigator, {\n        initialRouteName: \"Home\",\n        children: [_jsx(Stack.Screen, {\n          name: \"Home\",\n          component: HomeScreen,\n          options: {\n            title: 'GasLink'\n          }\n        }), _jsx(Stack.Screen, {\n          name: \"GasStationDetail\",\n          component: GasStationDetailScreen,\n          options: {\n            title: 'Gas Station Details'\n          }\n        }), _jsx(Stack.Screen, {\n          name: \"Search\",\n          component: SearchScreen,\n          options: {\n            title: 'Search Gas Stations'\n          }\n        }), _jsx(Stack.Screen, {\n          name: \"Profile\",\n          component: ProfileScreen,\n          options: {\n            title: 'My Profile'\n          }\n        }), _jsx(Stack.Screen, {\n          name: \"Login\",\n          component: LoginScreen,\n          options: {\n            title: 'Login'\n          }\n        }), _jsx(Stack.Screen, {\n          name: \"Register\",\n          component: RegisterScreen,\n          options: {\n            title: 'Register'\n          }\n        })]\n      })]\n    })\n  });\n}\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff'\n  }\n});", "map": {"version": 3, "names": ["React", "NavigationContainer", "createNativeStackNavigator", "StatusBar", "StyleSheet", "View", "HomeScreen", "GasStationDetailScreen", "SearchScreen", "ProfileScreen", "LoginScreen", "RegisterScreen", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "App", "children", "style", "styles", "container", "Navigator", "initialRouteName", "Screen", "name", "component", "options", "title", "create", "flex", "backgroundColor"], "sources": ["C:/Bagide/Clients/Carlos/GasLink/mobile/App.js"], "sourcesContent": ["import React from 'react';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createNativeStackNavigator } from '@react-navigation/native-stack';\nimport { StatusBar } from 'expo-status-bar';\nimport { StyleSheet, View } from 'react-native';\n\n// Screens\nimport HomeScreen from './screens/HomeScreen';\nimport GasStationDetailScreen from './screens/GasStationDetailScreen';\nimport SearchScreen from './screens/SearchScreen';\nimport ProfileScreen from './screens/ProfileScreen';\nimport LoginScreen from './screens/LoginScreen';\nimport RegisterScreen from './screens/RegisterScreen';\n\nconst Stack = createNativeStackNavigator();\n\nexport default function App() {\n  return (\n    <NavigationContainer>\n      <View style={styles.container}>\n        <StatusBar style=\"auto\" />\n        <Stack.Navigator initialRouteName=\"Home\">\n          <Stack.Screen \n            name=\"Home\" \n            component={HomeScreen} \n            options={{ title: 'GasLink' }}\n          />\n          <Stack.Screen \n            name=\"GasStationDetail\" \n            component={GasStationDetailScreen} \n            options={{ title: 'Gas Station Details' }}\n          />\n          <Stack.Screen \n            name=\"Search\" \n            component={SearchScreen} \n            options={{ title: 'Search Gas Stations' }}\n          />\n          <Stack.Screen \n            name=\"Profile\" \n            component={ProfileScreen} \n            options={{ title: 'My Profile' }}\n          />\n          <Stack.Screen \n            name=\"Login\" \n            component={LoginScreen} \n            options={{ title: 'Login' }}\n          />\n          <Stack.Screen \n            name=\"Register\" \n            component={RegisterScreen} \n            options={{ title: 'Register' }}\n          />\n        </Stack.Navigator>\n      </View>\n    </NavigationContainer>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n});"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,0BAA0B,QAAQ,gCAAgC;AAC3E,SAASC,SAAS,QAAQ,iBAAiB;AAAC,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAI5C,OAAOC,UAAU;AACjB,OAAOC,sBAAsB;AAC7B,OAAOC,YAAY;AACnB,OAAOC,aAAa;AACpB,OAAOC,WAAW;AAClB,OAAOC,cAAc;AAAiC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEtD,IAAMC,KAAK,GAAGd,0BAA0B,CAAC,CAAC;AAE1C,eAAe,SAASe,GAAGA,CAAA,EAAG;EAC5B,OACEJ,IAAA,CAACZ,mBAAmB;IAAAiB,QAAA,EAClBH,KAAA,CAACV,IAAI;MAACc,KAAK,EAAEC,MAAM,CAACC,SAAU;MAAAH,QAAA,GAC5BL,IAAA,CAACV,SAAS;QAACgB,KAAK,EAAC;MAAM,CAAE,CAAC,EAC1BJ,KAAA,CAACC,KAAK,CAACM,SAAS;QAACC,gBAAgB,EAAC,MAAM;QAAAL,QAAA,GACtCL,IAAA,CAACG,KAAK,CAACQ,MAAM;UACXC,IAAI,EAAC,MAAM;UACXC,SAAS,EAAEpB,UAAW;UACtBqB,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAU;QAAE,CAC/B,CAAC,EACFf,IAAA,CAACG,KAAK,CAACQ,MAAM;UACXC,IAAI,EAAC,kBAAkB;UACvBC,SAAS,EAAEnB,sBAAuB;UAClCoB,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAsB;QAAE,CAC3C,CAAC,EACFf,IAAA,CAACG,KAAK,CAACQ,MAAM;UACXC,IAAI,EAAC,QAAQ;UACbC,SAAS,EAAElB,YAAa;UACxBmB,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAsB;QAAE,CAC3C,CAAC,EACFf,IAAA,CAACG,KAAK,CAACQ,MAAM;UACXC,IAAI,EAAC,SAAS;UACdC,SAAS,EAAEjB,aAAc;UACzBkB,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAa;QAAE,CAClC,CAAC,EACFf,IAAA,CAACG,KAAK,CAACQ,MAAM;UACXC,IAAI,EAAC,OAAO;UACZC,SAAS,EAAEhB,WAAY;UACvBiB,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAQ;QAAE,CAC7B,CAAC,EACFf,IAAA,CAACG,KAAK,CAACQ,MAAM;UACXC,IAAI,EAAC,UAAU;UACfC,SAAS,EAAEf,cAAe;UAC1BgB,OAAO,EAAE;YAAEC,KAAK,EAAE;UAAW;QAAE,CAChC,CAAC;MAAA,CACa,CAAC;IAAA,CACd;EAAC,CACY,CAAC;AAE1B;AAEA,IAAMR,MAAM,GAAGhB,UAAU,CAACyB,MAAM,CAAC;EAC/BR,SAAS,EAAE;IACTS,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}